<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Directory Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #bee5eb;
            margin-top: 20px;
        }
        .next-steps {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="success">
        <h1>✅ SUCCESS: Directory is Accessible!</h1>
        <p>If you can see this page, the 403 Forbidden error has been resolved for this directory.</p>
    </div>

    <div class="info">
        <h2>📊 Test Results</h2>
        <p><strong>Directory:</strong> /referbot/management/</p>
        <p><strong>File Type:</strong> Static HTML</p>
        <p><strong>Status:</strong> Accessible</p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
    </div>

    <div class="next-steps">
        <h2>🚀 Next Steps</h2>
        <p>Now that directory access is confirmed, try these links:</p>
        <ul>
            <li><a href="test.php">PHP Test File</a> - Tests PHP execution</li>
            <li><a href="login.php">Admin Login Page</a> - Main admin interface</li>
            <li><a href="index.php">Admin Panel Home</a> - Dashboard redirect</li>
        </ul>
        
        <h3>🔐 Login Information</h3>
        <p><strong>Passcode:</strong> 1412</p>
        
        <h3>📱 Admin Panel Features</h3>
        <ul>
            <li>Real-time user statistics (100k+ users)</li>
            <li>Financial analytics and withdrawal tracking</li>
            <li>User management with search and filtering</li>
            <li>Leaderboards and performance metrics</li>
            <li>Secure session management</li>
        </ul>
    </div>

    <script>
        // Display current timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Auto-refresh every 30 seconds to show it's working
        setTimeout(function() {
            document.getElementById('timestamp').textContent = new Date().toLocaleString() + ' (Auto-refreshed)';
        }, 30000);
    </script>
</body>
</html>
