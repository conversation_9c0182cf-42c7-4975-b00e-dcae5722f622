<?php
// Simple PHP 8.2 compatible dashboard for testing
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check authentication
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    header('Location: simple_login.php');
    exit;
}

// Logout functionality
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple_login.php');
    exit;
}

// Try to load data safely
$stats = [
    'total_users' => 0,
    'total_withdrawals' => 0,
    'total_balance' => 0,
    'total_referrals' => 0
];

$data_loaded = false;
$storage_mode = 'unknown';

// Try to determine storage mode and load data
try {
    $data_dir = __DIR__ . '/../data/';
    $users_file = $data_dir . 'users.json';
    
    if (file_exists($users_file)) {
        $storage_mode = 'json';
        $users_data = file_get_contents($users_file);
        
        if ($users_data !== false) {
            $users = json_decode($users_data, true);
            
            if (is_array($users)) {
                $stats['total_users'] = count($users);
                
                foreach ($users as $user) {
                    $stats['total_balance'] += $user['balance'] ?? 0;
                    $stats['total_withdrawals'] += $user['successful_withdraw'] ?? 0;
                    $stats['total_referrals'] += count($user['promotion_report'] ?? []);
                }
                
                $data_loaded = true;
            }
        }
    } else {
        // Check if MySQL config exists
        $config_file = $data_dir . 'config.php';
        if (file_exists($config_file)) {
            $storage_mode = 'mysql (config found)';
        }
    }
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f6fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
        }
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .info-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .nav-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        .nav-link {
            background: #667eea;
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-link:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎛️ Simple Admin Dashboard</h1>
        <a href="?logout=1" class="logout-btn">🚪 Logout</a>
    </div>
    
    <?php if ($data_loaded): ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['total_users']); ?></div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₹<?php echo number_format($stats['total_balance'], 2); ?></div>
                <div class="stat-label">Total Balance</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₹<?php echo number_format($stats['total_withdrawals'], 2); ?></div>
                <div class="stat-label">Total Withdrawals</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($stats['total_referrals']); ?></div>
                <div class="stat-label">Total Referrals</div>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="info-section">
        <h3>🔧 System Information</h3>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>Storage Mode:</strong> <?php echo $storage_mode; ?></p>
        <p><strong>Data Status:</strong> 
            <?php if ($data_loaded): ?>
                <span class="success">✅ Data loaded successfully</span>
            <?php else: ?>
                <span class="warning">⚠️ No data loaded (this is normal for new installations)</span>
            <?php endif; ?>
        </p>
        <p><strong>Session Status:</strong> <span class="success">✅ Working</span></p>
        <p><strong>Login Time:</strong> <?php echo date('Y-m-d H:i:s', $_SESSION['admin_login_time']); ?></p>
    </div>
    
    <div class="info-section">
        <h3>🚀 Next Steps</h3>
        <p>This simple dashboard confirms that PHP and the admin panel are working correctly!</p>
        
        <div class="nav-links">
            <a href="test.php" class="nav-link">🧪 Run Tests</a>
            <a href="debug.php" class="nav-link">🔍 Debug Info</a>
            <a href="view_errors.php" class="nav-link">📋 View Logs</a>
            <a href="login.php" class="nav-link">🎨 Full Admin Panel</a>
        </div>
        
        <p style="margin-top: 1rem;"><strong>If this page loads correctly, your admin panel is working!</strong></p>
        <p>You can now try accessing the full admin panel at <code>login.php</code></p>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="info-section">
            <h3>⚠️ Error Details</h3>
            <p class="error"><?php echo htmlspecialchars($error_message); ?></p>
        </div>
    <?php endif; ?>
</body>
</html>
