        </div> <!-- End content-area -->
    </div> <!-- End main-content -->
    
    <!-- Loading Overlay -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading data...</p>
    </div>
    
    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global admin panel utilities
        const AdminPanel = {
            // Show loading spinner
            showLoading: function() {
                document.getElementById('loadingSpinner').classList.add('show');
            },
            
            // Hide loading spinner
            hideLoading: function() {
                document.getElementById('loadingSpinner').classList.remove('show');
            },
            
            // Format currency
            formatCurrency: function(amount) {
                return '₹' + new Intl.NumberFormat('en-IN').format(amount);
            },
            
            // Format numbers with K/M suffixes
            formatNumber: function(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toString();
            },
            
            // Show toast notification
            showToast: function(message, type = 'info') {
                const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();
                const toast = this.createToast(message, type);
                toastContainer.appendChild(toast);
                
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                // Remove toast after it's hidden
                toast.addEventListener('hidden.bs.toast', function() {
                    toast.remove();
                });
            },
            
            // Create toast container if it doesn't exist
            createToastContainer: function() {
                const container = document.createElement('div');
                container.id = 'toastContainer';
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
                return container;
            },
            
            // Create toast element
            createToast: function(message, type) {
                const toast = document.createElement('div');
                toast.className = 'toast';
                toast.setAttribute('role', 'alert');
                
                const iconMap = {
                    'success': 'fas fa-check-circle text-success',
                    'error': 'fas fa-exclamation-circle text-danger',
                    'warning': 'fas fa-exclamation-triangle text-warning',
                    'info': 'fas fa-info-circle text-info'
                };
                
                const icon = iconMap[type] || iconMap['info'];
                
                toast.innerHTML = `
                    <div class="toast-header">
                        <i class="${icon} me-2"></i>
                        <strong class="me-auto">Admin Panel</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                `;
                
                return toast;
            },
            
            // AJAX helper
            ajax: function(url, options = {}) {
                const defaults = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                };
                
                const config = Object.assign(defaults, options);
                
                this.showLoading();
                
                return fetch(url, config)
                    .then(response => {
                        this.hideLoading();
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .catch(error => {
                        this.hideLoading();
                        this.showToast('An error occurred: ' + error.message, 'error');
                        throw error;
                    });
            },
            
            // Confirm dialog
            confirm: function(message, callback) {
                if (confirm(message)) {
                    callback();
                }
            },
            
            // Auto-refresh functionality
            autoRefresh: function(interval = 30000) {
                setInterval(() => {
                    if (document.visibilityState === 'visible') {
                        location.reload();
                    }
                }, interval);
            }
        };
        
        // Sidebar toggle for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !menuBtn.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.transition = 'opacity 0.5s ease';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }, 5000);
            });
        });
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // Table row click handlers
        document.addEventListener('DOMContentLoaded', function() {
            const clickableRows = document.querySelectorAll('.table-row-clickable');
            clickableRows.forEach(row => {
                row.style.cursor = 'pointer';
                row.addEventListener('click', function() {
                    const url = this.dataset.href;
                    if (url) {
                        window.location.href = url;
                    }
                });
            });
        });
        
        // Search functionality with debounce
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Initialize search if search input exists
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                const debouncedSearch = debounce(function() {
                    const form = searchInput.closest('form');
                    if (form) {
                        form.submit();
                    }
                }, 500);
                
                searchInput.addEventListener('input', debouncedSearch);
            }
        });
        
        // Chart.js default configuration
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            Chart.defaults.color = '#6c757d';
            Chart.defaults.plugins.legend.labels.usePointStyle = true;
            Chart.defaults.plugins.legend.labels.padding = 20;
            Chart.defaults.elements.arc.borderWidth = 0;
            Chart.defaults.elements.bar.borderRadius = 4;
            Chart.defaults.elements.line.tension = 0.4;
        }
        
        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('Page load time:', loadTime + 'ms');
            
            // Log slow page loads
            if (loadTime > 3000) {
                console.warn('Slow page load detected:', loadTime + 'ms');
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Escape to close modals/dropdowns
            if (e.key === 'Escape') {
                const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                openDropdowns.forEach(dropdown => {
                    const toggle = dropdown.previousElementSibling;
                    if (toggle) {
                        bootstrap.Dropdown.getInstance(toggle)?.hide();
                    }
                });
            }
        });
    </script>
    
    <!-- Page-specific JavaScript -->
    <?php if (isset($pageScript)): ?>
        <script><?php echo $pageScript; ?></script>
    <?php endif; ?>
    
</body>
</html>
