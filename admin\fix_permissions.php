<?php
// Permission fixing script for <PERSON>inger shared hosting
echo "<h2>Fixing File Permissions</h2>";
echo "<hr>";

// Files that need 644 permissions
$files = [
    'config.php',
    'login.php',
    'dashboard.php',
    'users.php',
    'finance.php',
    'leaderboards.php',
    'statistics.php',
    'settings.php',
    'user_details.php',
    'data_access.php',
    'index.php',
    'logout.php',
    'debug.php',
    'fix_permissions.php',
    'includes/header.php',
    'includes/footer.php',
    'README.md'
];

// Directories that need 755 permissions
$directories = [
    '.',
    'includes',
    'cache'
];

echo "<h3>Setting Directory Permissions (755)</h3>";
foreach ($directories as $dir) {
    $path = __DIR__ . '/' . $dir;
    if ($dir === '.') {
        $path = __DIR__;
    }
    
    if (is_dir($path)) {
        if (chmod($path, 0755)) {
            echo "✓ Set 755 for directory: $dir<br>";
        } else {
            echo "✗ Failed to set permissions for directory: $dir<br>";
        }
    } else {
        if ($dir !== '.' && !is_dir($path)) {
            if (mkdir($path, 0755, true)) {
                echo "✓ Created directory with 755: $dir<br>";
            } else {
                echo "✗ Failed to create directory: $dir<br>";
            }
        }
    }
}

echo "<br><h3>Setting File Permissions (644)</h3>";
foreach ($files as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        if (chmod($path, 0644)) {
            echo "✓ Set 644 for file: $file<br>";
        } else {
            echo "✗ Failed to set permissions for file: $file<br>";
        }
    } else {
        echo "⚠ File not found: $file<br>";
    }
}

echo "<br><h3>Creating Missing Directories</h3>";
$required_dirs = ['cache', 'includes'];
foreach ($required_dirs as $dir) {
    $path = __DIR__ . '/' . $dir;
    if (!is_dir($path)) {
        if (mkdir($path, 0755, true)) {
            echo "✓ Created directory: $dir<br>";
        } else {
            echo "✗ Failed to create directory: $dir<br>";
        }
    } else {
        echo "✓ Directory exists: $dir<br>";
    }
}

echo "<br><h3>Creating .htaccess for Security</h3>";
$htaccess_content = "# Admin Panel Security
Options -Indexes
DirectoryIndex index.php

# Protect sensitive files
<Files \"config.php\">
    Order allow,deny
    Deny from all
</Files>

<Files \"data_access.php\">
    Order allow,deny
    Deny from all
</Files>

<Files \"debug.php\">
    Order allow,deny
    Allow from all
</Files>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css \"access plus 1 month\"
    ExpiresByType application/javascript \"access plus 1 month\"
    ExpiresByType image/png \"access plus 1 month\"
    ExpiresByType image/jpg \"access plus 1 month\"
    ExpiresByType image/jpeg \"access plus 1 month\"
    ExpiresByType image/gif \"access plus 1 month\"
</IfModule>";

if (file_put_contents(__DIR__ . '/.htaccess', $htaccess_content)) {
    echo "✓ Created .htaccess file<br>";
} else {
    echo "✗ Failed to create .htaccess file<br>";
}

echo "<br><hr>";
echo "<p><strong>Permissions fixed!</strong> Try accessing the admin panel again.</p>";
echo "<p>If you still get errors, check the debug.php output for more details.</p>";
?>
