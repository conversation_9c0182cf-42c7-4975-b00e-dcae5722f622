<?php
$pageTitle = 'User Details';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();
$userId = $_GET['id'] ?? null;

if (!$userId) {
    header('Location: users.php');
    exit;
}

$user = $dataAccess->getUserDetails($userId);

if (!$user) {
    header('Location: users.php?error=user_not_found');
    exit;
}

// Calculate user metrics
$totalReferrals = count($user['promotion_report'] ?? []);
$totalReferralEarnings = array_sum(array_column($user['promotion_report'] ?? [], 'amount_got'));
$joinDate = $user['created_at'] ?? 'Unknown';
$lastActivity = $user['updated_at'] ?? $joinDate;
?>

<!-- User Header -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start">
                <div class="d-flex align-items-center">
                    <div class="user-avatar me-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <h2 class="mb-1"><?php echo htmlspecialchars($user['first_name']); ?></h2>
                        <div class="user-meta">
                            <?php if (!empty($user['username'])): ?>
                                <span class="badge bg-primary me-2">@<?php echo htmlspecialchars($user['username']); ?></span>
                            <?php endif; ?>
                            <span class="text-muted">ID: <?php echo $user['user_id']; ?></span>
                        </div>
                        <div class="user-status mt-2">
                            <?php if ($user['banned'] ?? false): ?>
                                <span class="badge bg-danger">
                                    <i class="fas fa-ban me-1"></i>Banned
                                </span>
                            <?php else: ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Active
                                </span>
                            <?php endif; ?>
                            
                            <?php if ($user['referred'] ?? false): ?>
                                <span class="badge bg-info ms-2">
                                    <i class="fas fa-user-friends me-1"></i>Referred User
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="value"><?php echo formatCurrency($user['balance'] ?? 0); ?></div>
            <div class="label">Current Balance</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="value"><?php echo formatCurrency($user['successful_withdraw'] ?? 0); ?></div>
            <div class="label">Total Withdrawn</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <i class="fas fa-share-alt"></i>
            </div>
            <div class="value"><?php echo number_format($totalReferrals); ?></div>
            <div class="label">Total Referrals</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-coins"></i>
            </div>
            <div class="value"><?php echo formatCurrency($totalReferralEarnings); ?></div>
            <div class="label">Referral Earnings</div>
        </div>
    </div>
</div>

<!-- User Information Tabs -->
<div class="row g-4">
    <div class="col-12">
        <div class="stats-card">
            <ul class="nav nav-tabs mb-4" id="userTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-info-circle me-2"></i>Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab">
                        <i class="fas fa-user-cog me-2"></i>Account Info
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="referrals-tab" data-bs-toggle="tab" data-bs-target="#referrals" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>Referrals (<?php echo $totalReferrals; ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                        <i class="fas fa-history me-2"></i>Activity
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="userTabContent">
                <!-- Overview Tab -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Basic Information</h6>
                            <div class="info-list">
                                <div class="info-item">
                                    <span class="info-label">User ID:</span>
                                    <span class="info-value"><?php echo $user['user_id']; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">First Name:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['first_name']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Username:</span>
                                    <span class="info-value">
                                        <?php echo !empty($user['username']) ? '@' . htmlspecialchars($user['username']) : 'Not set'; ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Join Date:</span>
                                    <span class="info-value"><?php echo date('M d, Y H:i', strtotime($joinDate)); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Last Activity:</span>
                                    <span class="info-value"><?php echo timeAgo($lastActivity); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Financial Summary</h6>
                            <div class="info-list">
                                <div class="info-item">
                                    <span class="info-label">Current Balance:</span>
                                    <span class="info-value text-success fw-bold"><?php echo formatCurrency($user['balance'] ?? 0); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Joining Bonus:</span>
                                    <span class="info-value"><?php echo formatCurrency($user['joining_bonus_got'] ?? 0); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Successful Withdrawals:</span>
                                    <span class="info-value"><?php echo formatCurrency($user['successful_withdraw'] ?? 0); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Pending Withdrawals:</span>
                                    <span class="info-value text-warning"><?php echo formatCurrency($user['withdraw_under_review'] ?? 0); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Referral Earnings:</span>
                                    <span class="info-value text-info"><?php echo formatCurrency($totalReferralEarnings); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Account Info Tab -->
                <div class="tab-pane fade" id="account" role="tabpanel">
                    <h6 class="text-muted mb-3">Account Information</h6>
                    <?php 
                    $accountInfo = $user['account_info'] ?? [];
                    if (empty($accountInfo) && isset($user['account_name'])) {
                        $accountInfo = [
                            'name' => $user['account_name'] ?? '',
                            'ifsc' => $user['ifsc'] ?? '',
                            'email' => $user['email'] ?? '',
                            'account_number' => $user['account_number'] ?? '',
                            'mobile_number' => $user['mobile_number'] ?? '',
                            'usdt_address' => $user['usdt_address'] ?? '',
                            'withdrawal_method' => $user['withdrawal_method'] ?? 'bank'
                        ];
                    }
                    ?>
                    
                    <?php if (!empty($accountInfo)): ?>
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="info-label">Account Name:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($accountInfo['name'] ?? 'Not provided'); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Email:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($accountInfo['email'] ?? 'Not provided'); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Mobile Number:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($accountInfo['mobile_number'] ?? 'Not provided'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="info-label">Withdrawal Method:</span>
                                        <span class="info-value">
                                            <span class="badge bg-<?php echo ($accountInfo['withdrawal_method'] ?? 'bank') === 'bank' ? 'primary' : 'warning'; ?>">
                                                <?php echo strtoupper($accountInfo['withdrawal_method'] ?? 'bank'); ?>
                                            </span>
                                        </span>
                                    </div>
                                    <?php if (($accountInfo['withdrawal_method'] ?? 'bank') === 'bank'): ?>
                                        <div class="info-item">
                                            <span class="info-label">Account Number:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($accountInfo['account_number'] ?? 'Not provided'); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">IFSC Code:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($accountInfo['ifsc'] ?? 'Not provided'); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <div class="info-item">
                                            <span class="info-label">USDT Address:</span>
                                            <span class="info-value small"><?php echo htmlspecialchars($accountInfo['usdt_address'] ?? 'Not provided'); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-cog fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No account information provided yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Referrals Tab -->
                <div class="tab-pane fade" id="referrals" role="tabpanel">
                    <h6 class="text-muted mb-3">Referral History</h6>
                    <?php if (!empty($user['promotion_report'])): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Referred User</th>
                                        <th>User ID</th>
                                        <th>Earnings</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($user['promotion_report'] as $referral): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?php echo htmlspecialchars($referral['referred_user_name']); ?></div>
                                            </td>
                                            <td>
                                                <span class="text-muted"><?php echo $referral['referred_user_id']; ?></span>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold"><?php echo formatCurrency($referral['amount_got']); ?></span>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    <?php echo isset($referral['created_at']) ? timeAgo($referral['created_at']) : 'Unknown'; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No referrals yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Activity Tab -->
                <div class="tab-pane fade" id="activity" role="tabpanel">
                    <h6 class="text-muted mb-3">Recent Activity</h6>
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">User Registered</div>
                                <div class="activity-time"><?php echo timeAgo($joinDate); ?></div>
                            </div>
                        </div>
                        
                        <?php if ($totalReferrals > 0): ?>
                            <div class="activity-item">
                                <div class="activity-icon bg-success">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Made <?php echo $totalReferrals; ?> referral<?php echo $totalReferrals > 1 ? 's' : ''; ?></div>
                                    <div class="activity-time">Earned <?php echo formatCurrency($totalReferralEarnings); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (($user['successful_withdraw'] ?? 0) > 0): ?>
                            <div class="activity-item">
                                <div class="activity-icon bg-info">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Successful Withdrawal</div>
                                    <div class="activity-time"><?php echo formatCurrency($user['successful_withdraw']); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.user-meta {
    margin-bottom: 0.5rem;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 150px;
}

.info-value {
    flex: 1;
    text-align: right;
}

.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--dark-color);
}

.activity-time {
    font-size: 0.9rem;
    color: #6c757d;
}

.nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}
</style>

<?php require_once 'includes/footer.php'; ?>
