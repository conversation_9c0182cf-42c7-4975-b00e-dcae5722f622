<?php
// Simple PHP 8.2 compatible login page for troubleshooting
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple configuration
$ADMIN_PASSCODE = '1412';
$SESSION_TIMEOUT = 3600; // 1 hour

// Check if already authenticated
if (isset($_SESSION['admin_authenticated']) && 
    $_SESSION['admin_authenticated'] === true &&
    isset($_SESSION['admin_login_time']) &&
    (time() - $_SESSION['admin_login_time']) < $SESSION_TIMEOUT) {
    header('Location: simple_dashboard.php');
    exit;
}

$error = '';

if ($_POST) {
    $passcode = $_POST['passcode'] ?? '';
    
    if ($passcode === $ADMIN_PASSCODE) {
        $_SESSION['admin_authenticated'] = true;
        $_SESSION['admin_login_time'] = time();
        header('Location: simple_dashboard.php');
        exit;
    } else {
        $error = 'Invalid passcode. Please try again.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h2 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
        .error {
            background: #ffe6e6;
            color: #d00;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #ffcccc;
        }
        .info {
            background: #e6f3ff;
            color: #0066cc;
            padding: 0.75rem;
            border-radius: 5px;
            margin-top: 1rem;
            border: 1px solid #cce6ff;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>🔐 Simple Admin Login</h2>
            <p>Referral Bot Admin Panel</p>
        </div>
        
        <?php if ($error): ?>
            <div class="error">
                ⚠️ <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="passcode">Access Passcode</label>
                <input type="password" 
                       class="form-control" 
                       id="passcode" 
                       name="passcode" 
                       placeholder="Enter your passcode"
                       required 
                       autocomplete="off">
            </div>
            
            <button type="submit" class="btn">
                🚀 Access Admin Panel
            </button>
        </form>
        
        <div class="info">
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
            <strong>Status:</strong> ✅ Simple login working
        </div>
    </div>
</body>
</html>
