<?php
$pageTitle = 'Leaderboards';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();
$leaderboards = $dataAccess->getLeaderboards();

// Helper function to get rank icon
function getRankIcon($rank) {
    switch ($rank) {
        case 1: return '<i class="fas fa-trophy text-warning"></i>';
        case 2: return '<i class="fas fa-medal text-secondary"></i>';
        case 3: return '<i class="fas fa-award text-warning"></i>';
        default: return '<span class="rank-number">' . $rank . '</span>';
    }
}

// Helper function to get rank class
function getRankClass($rank) {
    switch ($rank) {
        case 1: return 'rank-gold';
        case 2: return 'rank-silver';
        case 3: return 'rank-bronze';
        default: return '';
    }
}
?>

<!-- Leaderboard Header -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="stats-card text-center">
            <h2 class="mb-3">
                <i class="fas fa-trophy me-3 text-warning"></i>
                Leaderboards
            </h2>
            <p class="text-muted mb-0">
                Top performers in referrals, withdrawals, and account balances
            </p>
        </div>
    </div>
</div>

<!-- Leaderboard Tabs -->
<div class="row g-4">
    <div class="col-12">
        <div class="stats-card">
            <ul class="nav nav-pills nav-fill mb-4" id="leaderboardTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="referrers-tab" data-bs-toggle="pill" data-bs-target="#referrers" type="button" role="tab">
                        <i class="fas fa-share-alt me-2"></i>
                        Top Referrers
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="withdrawers-tab" data-bs-toggle="pill" data-bs-target="#withdrawers" type="button" role="tab">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Top Withdrawers
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="balances-tab" data-bs-toggle="pill" data-bs-target="#balances" type="button" role="tab">
                        <i class="fas fa-wallet me-2"></i>
                        Highest Balances
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="leaderboardTabContent">
                <!-- Top Referrers -->
                <div class="tab-pane fade show active" id="referrers" role="tabpanel">
                    <div class="leaderboard-header mb-4">
                        <h4>
                            <i class="fas fa-share-alt me-2 text-primary"></i>
                            Top Referrers
                        </h4>
                        <p class="text-muted mb-0">Users with the most successful referrals</p>
                    </div>
                    
                    <?php if (empty($leaderboards['top_referrers'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No referral data available yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="leaderboard-list">
                            <?php foreach ($leaderboards['top_referrers'] as $index => $user): ?>
                                <div class="leaderboard-item <?php echo getRankClass($index + 1); ?>">
                                    <div class="rank">
                                        <?php echo getRankIcon($index + 1); ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name"><?php echo htmlspecialchars($user['first_name']); ?></div>
                                        <div class="user-details">
                                            <?php if (!empty($user['username'])): ?>
                                                @<?php echo htmlspecialchars($user['username']); ?>
                                            <?php endif; ?>
                                            <span class="text-muted">• ID: <?php echo $user['user_id']; ?></span>
                                        </div>
                                    </div>
                                    <div class="score">
                                        <div class="score-value"><?php echo number_format($user['referral_count']); ?></div>
                                        <div class="score-label">Referrals</div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Top Withdrawers -->
                <div class="tab-pane fade" id="withdrawers" role="tabpanel">
                    <div class="leaderboard-header mb-4">
                        <h4>
                            <i class="fas fa-money-bill-wave me-2 text-success"></i>
                            Top Withdrawers
                        </h4>
                        <p class="text-muted mb-0">Users with the highest withdrawal amounts</p>
                    </div>
                    
                    <?php if (empty($leaderboards['top_withdrawers'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No withdrawal data available yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="leaderboard-list">
                            <?php foreach ($leaderboards['top_withdrawers'] as $index => $user): ?>
                                <div class="leaderboard-item <?php echo getRankClass($index + 1); ?>">
                                    <div class="rank">
                                        <?php echo getRankIcon($index + 1); ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name"><?php echo htmlspecialchars($user['first_name']); ?></div>
                                        <div class="user-details">
                                            <?php if (!empty($user['username'])): ?>
                                                @<?php echo htmlspecialchars($user['username']); ?>
                                            <?php endif; ?>
                                            <span class="text-muted">• ID: <?php echo $user['user_id']; ?></span>
                                        </div>
                                    </div>
                                    <div class="score">
                                        <div class="score-value"><?php echo formatCurrency($user['successful_withdraw']); ?></div>
                                        <div class="score-label">Withdrawn</div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Highest Balances -->
                <div class="tab-pane fade" id="balances" role="tabpanel">
                    <div class="leaderboard-header mb-4">
                        <h4>
                            <i class="fas fa-wallet me-2 text-info"></i>
                            Highest Balances
                        </h4>
                        <p class="text-muted mb-0">Users with the highest current account balances</p>
                    </div>
                    
                    <?php if (empty($leaderboards['top_balances'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-wallet fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No balance data available yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="leaderboard-list">
                            <?php foreach ($leaderboards['top_balances'] as $index => $user): ?>
                                <div class="leaderboard-item <?php echo getRankClass($index + 1); ?>">
                                    <div class="rank">
                                        <?php echo getRankIcon($index + 1); ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name"><?php echo htmlspecialchars($user['first_name']); ?></div>
                                        <div class="user-details">
                                            <?php if (!empty($user['username'])): ?>
                                                @<?php echo htmlspecialchars($user['username']); ?>
                                            <?php endif; ?>
                                            <span class="text-muted">• ID: <?php echo $user['user_id']; ?></span>
                                        </div>
                                    </div>
                                    <div class="score">
                                        <div class="score-value"><?php echo formatCurrency($user['balance']); ?></div>
                                        <div class="score-label">Balance</div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaderboard Statistics -->
<div class="row g-4 mt-4">
    <div class="col-lg-4">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; width: 60px; height: 60px;">
                <i class="fas fa-crown"></i>
            </div>
            <h5>Top Referrer</h5>
            <?php if (!empty($leaderboards['top_referrers'])): ?>
                <p class="mb-1 fw-bold"><?php echo htmlspecialchars($leaderboards['top_referrers'][0]['first_name']); ?></p>
                <p class="text-muted small"><?php echo number_format($leaderboards['top_referrers'][0]['referral_count']); ?> referrals</p>
            <?php else: ?>
                <p class="text-muted">No data available</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; width: 60px; height: 60px;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h5>Top Withdrawer</h5>
            <?php if (!empty($leaderboards['top_withdrawers'])): ?>
                <p class="mb-1 fw-bold"><?php echo htmlspecialchars($leaderboards['top_withdrawers'][0]['first_name']); ?></p>
                <p class="text-muted small"><?php echo formatCurrency($leaderboards['top_withdrawers'][0]['successful_withdraw']); ?></p>
            <?php else: ?>
                <p class="text-muted">No data available</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="stats-card text-center">
            <div class="icon mx-auto mb-3" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white; width: 60px; height: 60px;">
                <i class="fas fa-gem"></i>
            </div>
            <h5>Highest Balance</h5>
            <?php if (!empty($leaderboards['top_balances'])): ?>
                <p class="mb-1 fw-bold"><?php echo htmlspecialchars($leaderboards['top_balances'][0]['first_name']); ?></p>
                <p class="text-muted small"><?php echo formatCurrency($leaderboards['top_balances'][0]['balance']); ?></p>
            <?php else: ?>
                <p class="text-muted">No data available</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.nav-pills .nav-link {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.leaderboard-header h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.leaderboard-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.leaderboard-item.rank-gold {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.leaderboard-item.rank-silver {
    background: linear-gradient(135deg, #e2e3e5 0%, #d1ecf1 100%);
    border-color: #6c757d;
}

.leaderboard-item.rank-bronze {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #cd853f;
}

.rank {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1.5rem;
}

.rank-number {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.user-details {
    font-size: 0.9rem;
    color: #6c757d;
}

.score {
    text-align: right;
}

.score-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.score-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@media (max-width: 768px) {
    .leaderboard-item {
        padding: 1rem;
    }
    
    .rank {
        width: 40px;
        height: 40px;
        margin-right: 1rem;
        font-size: 1.2rem;
    }
    
    .user-name {
        font-size: 1rem;
    }
    
    .score-value {
        font-size: 1.2rem;
    }
}
</style>

<?php
$pageScript = "
// Auto-refresh leaderboards every 10 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 600000);

// Add click handlers for leaderboard items
document.querySelectorAll('.leaderboard-item').forEach(item => {
    item.style.cursor = 'pointer';
    item.addEventListener('click', function() {
        const userIdMatch = this.textContent.match(/ID: (\d+)/);
        if (userIdMatch) {
            window.location.href = 'user_details.php?id=' + userIdMatch[1];
        }
    });
});
";

require_once 'includes/footer.php';
?>
