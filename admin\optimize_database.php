<?php
// Database Optimization Script for High Performance
// This script creates optimized indexes and views for handling 100k+ users

require_once 'config.php';

// Only allow execution if MySQL mode is enabled
if (STORAGE_MODE !== 'mysql') {
    die("This optimization script only works with MySQL storage mode.\n");
}

try {
    $pdo = getAdminDB();
    
    echo "Starting database optimization for high-performance admin panel...\n\n";
    
    // Create optimized indexes for better performance
    $indexes = [
        // Users table indexes
        "CREATE INDEX IF NOT EXISTS idx_users_balance_desc ON users(balance DESC)",
        "CREATE INDEX IF NOT EXISTS idx_users_successful_withdraw_desc ON users(successful_withdraw DESC)",
        "CREATE INDEX IF NOT EXISTS idx_users_withdraw_under_review ON users(withdraw_under_review)",
        "CREATE INDEX IF NOT EXISTS idx_users_banned_created ON users(banned, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_referred_created ON users(referred, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_first_name ON users(first_name)",
        "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
        "CREATE INDEX IF NOT EXISTS idx_users_created_date ON users(DATE(created_at))",
        
        // Promotion reports indexes
        "CREATE INDEX IF NOT EXISTS idx_promotion_referrer_created ON promotion_reports(referrer_id, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_promotion_referred_user ON promotion_reports(referred_user_id)",
        "CREATE INDEX IF NOT EXISTS idx_promotion_amount ON promotion_reports(amount_got)",
        "CREATE INDEX IF NOT EXISTS idx_promotion_created_date ON promotion_reports(DATE(created_at))",
        
        // Withdrawal reports indexes
        "CREATE INDEX IF NOT EXISTS idx_withdrawal_user_status ON withdrawal_reports(user_id, status)",
        "CREATE INDEX IF NOT EXISTS idx_withdrawal_status_amount ON withdrawal_reports(status, amount)",
        "CREATE INDEX IF NOT EXISTS idx_withdrawal_date_status ON withdrawal_reports(date, status)",
        "CREATE INDEX IF NOT EXISTS idx_withdrawal_created_date ON withdrawal_reports(DATE(created_at))",
        
        // User accounts indexes
        "CREATE INDEX IF NOT EXISTS idx_user_accounts_mobile ON user_accounts(mobile_number)",
        "CREATE INDEX IF NOT EXISTS idx_user_accounts_email ON user_accounts(email)",
        "CREATE INDEX IF NOT EXISTS idx_user_accounts_method ON user_accounts(withdrawal_method)",
        
        // Task submissions indexes
        "CREATE INDEX IF NOT EXISTS idx_task_submissions_status_date ON task_submissions(status, submitted_at)",
        "CREATE INDEX IF NOT EXISTS idx_task_submissions_user_task ON task_submissions(user_id, task_id)",
        
        // Admin settings indexes
        "CREATE INDEX IF NOT EXISTS idx_admin_settings_updated ON admin_settings(updated_at)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
            echo "✓ Created index: " . substr($index, strpos($index, 'idx_')) . "\n";
        } catch (PDOException $e) {
            echo "⚠ Index may already exist: " . substr($index, strpos($index, 'idx_')) . "\n";
        }
    }
    
    echo "\n";
    
    // Create optimized views for admin panel
    $views = [
        // Enhanced user stats view with better performance
        "CREATE OR REPLACE VIEW admin_user_stats AS
        SELECT
            u.user_id,
            u.first_name,
            u.username,
            u.balance,
            u.successful_withdraw,
            u.withdraw_under_review,
            u.joining_bonus_got,
            u.referred,
            u.referred_by,
            u.banned,
            u.created_at,
            u.updated_at,
            ua.name as account_name,
            ua.mobile_number,
            ua.email,
            ua.withdrawal_method,
            COALESCE(pr.total_referrals, 0) as total_referrals,
            COALESCE(pr.total_referral_earnings, 0) as total_referral_earnings,
            COALESCE(wr.pending_withdrawals, 0) as pending_withdrawal_requests,
            CASE 
                WHEN u.created_at >= CURDATE() THEN 1 
                ELSE 0 
            END as is_new_today,
            CASE 
                WHEN u.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 
                ELSE 0 
            END as is_active_7d,
            CASE 
                WHEN u.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 
                ELSE 0 
            END as is_active_30d
        FROM users u
        LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
        LEFT JOIN (
            SELECT 
                referrer_id,
                COUNT(*) as total_referrals,
                SUM(amount_got) as total_referral_earnings
            FROM promotion_reports 
            GROUP BY referrer_id
        ) pr ON u.user_id = pr.referrer_id
        LEFT JOIN (
            SELECT 
                user_id,
                COUNT(*) as pending_withdrawals
            FROM withdrawal_reports 
            WHERE status = 'Under review'
            GROUP BY user_id
        ) wr ON u.user_id = wr.user_id",
        
        // Fast dashboard stats view
        "CREATE OR REPLACE VIEW admin_dashboard_fast AS
        SELECT
            (SELECT COUNT(*) FROM users) as total_users,
            (SELECT COUNT(*) FROM users WHERE banned = TRUE) as banned_users,
            (SELECT COUNT(*) FROM users WHERE created_at >= CURDATE()) as new_users_today,
            (SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as active_users_7d,
            (SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as active_users_30d,
            (SELECT COALESCE(SUM(balance), 0) FROM users) as total_user_balance,
            (SELECT COALESCE(SUM(successful_withdraw), 0) FROM users) as total_successful_withdrawals,
            (SELECT COALESCE(SUM(withdraw_under_review), 0) FROM users) as total_pending_withdrawals,
            (SELECT COUNT(*) FROM promotion_reports) as total_referrals,
            (SELECT COUNT(*) FROM withdrawal_reports WHERE status = 'Under review') as pending_withdrawal_requests,
            (SELECT COALESCE(SUM(amount), 0) FROM withdrawal_reports WHERE status = 'Under review') as pending_withdrawal_amount",
        
        // Top performers view for leaderboards
        "CREATE OR REPLACE VIEW admin_top_performers AS
        SELECT
            'referrer' as type,
            u.user_id,
            u.first_name,
            u.username,
            COUNT(pr.id) as score,
            'referrals' as score_type
        FROM users u
        INNER JOIN promotion_reports pr ON u.user_id = pr.referrer_id
        GROUP BY u.user_id, u.first_name, u.username
        HAVING COUNT(pr.id) > 0
        
        UNION ALL
        
        SELECT
            'withdrawer' as type,
            user_id,
            first_name,
            username,
            successful_withdraw as score,
            'amount' as score_type
        FROM users
        WHERE successful_withdraw > 0
        
        UNION ALL
        
        SELECT
            'balance' as type,
            user_id,
            first_name,
            username,
            balance as score,
            'amount' as score_type
        FROM users
        WHERE balance > 0"
    ];
    
    foreach ($views as $view) {
        try {
            $pdo->exec($view);
            $viewName = preg_match('/VIEW\s+(\w+)/', $view, $matches) ? $matches[1] : 'unknown';
            echo "✓ Created view: $viewName\n";
        } catch (PDOException $e) {
            echo "✗ Error creating view: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // Optimize table storage engines and settings
    $optimizations = [
        "OPTIMIZE TABLE users",
        "OPTIMIZE TABLE user_accounts", 
        "OPTIMIZE TABLE promotion_reports",
        "OPTIMIZE TABLE withdrawal_reports",
        "OPTIMIZE TABLE admin_settings"
    ];
    
    foreach ($optimizations as $optimization) {
        try {
            $pdo->exec($optimization);
            $tableName = explode(' ', $optimization)[2];
            echo "✓ Optimized table: $tableName\n";
        } catch (PDOException $e) {
            echo "⚠ Could not optimize table: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // Create summary statistics table for caching
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_stats_cache (
            cache_key VARCHAR(100) PRIMARY KEY,
            cache_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            INDEX idx_cache_expires (expires_at)
        )
    ");
    echo "✓ Created admin stats cache table\n";
    
    // Set MySQL performance variables for better handling of large datasets
    $performanceSettings = [
        "SET SESSION query_cache_type = ON",
        "SET SESSION query_cache_size = 67108864", // 64MB
        "SET SESSION sort_buffer_size = 2097152",   // 2MB
        "SET SESSION read_buffer_size = 131072",    // 128KB
        "SET SESSION join_buffer_size = 262144",    // 256KB
        "SET SESSION tmp_table_size = 33554432",    // 32MB
        "SET SESSION max_heap_table_size = 33554432" // 32MB
    ];
    
    foreach ($performanceSettings as $setting) {
        try {
            $pdo->exec($setting);
        } catch (PDOException $e) {
            // Some settings might not be available depending on MySQL configuration
        }
    }
    echo "✓ Applied performance settings\n";
    
    echo "\n=== Database Optimization Complete ===\n";
    echo "Your database is now optimized for handling 100,000+ users efficiently.\n";
    echo "Key optimizations applied:\n";
    echo "• Created " . count($indexes) . " performance indexes\n";
    echo "• Created " . count($views) . " optimized views\n";
    echo "• Optimized table storage\n";
    echo "• Configured performance settings\n";
    echo "• Set up admin stats caching\n\n";
    
    // Display some performance statistics
    $stmt = $pdo->query("SELECT COUNT(*) as user_count FROM users");
    $userCount = $stmt->fetch()['user_count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as referral_count FROM promotion_reports");
    $referralCount = $stmt->fetch()['referral_count'];
    
    echo "Current database size:\n";
    echo "• Users: " . number_format($userCount) . "\n";
    echo "• Referrals: " . number_format($referralCount) . "\n";
    
    if ($userCount > 50000) {
        echo "\n⚡ High-volume database detected! All optimizations are especially important.\n";
    }
    
} catch (PDOException $e) {
    echo "✗ Database optimization failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
