<?php
// Verification Script for Test Data Generation
// This script verifies the fake referral data for user 1381431908

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load users data
$users_file = __DIR__ . '/../data/users.json';
$users = json_decode(file_get_contents($users_file), true);

$target_user_id = '1381431908';
$target_user = $users[$target_user_id] ?? null;

if (!$target_user) {
    die("Target user not found!\n");
}

echo "🎉 TEST DATA VERIFICATION REPORT\n";
echo "================================\n\n";

// Verify target user data
echo "👤 TARGET USER INFORMATION:\n";
echo "   User ID: $target_user_id\n";
echo "   Name: {$target_user['first_name']}\n";
echo "   Username: {$target_user['username']}\n";
echo "   Current Balance: ₹" . number_format($target_user['balance'], 2) . "\n";
echo "   Total Referrals: " . count($target_user['promotion_report']) . "\n\n";

// Calculate referral earnings
$total_referral_earnings = array_sum(array_column($target_user['promotion_report'], 'amount_got'));
echo "💰 REFERRAL EARNINGS:\n";
echo "   Total Earnings: ₹" . number_format($total_referral_earnings, 2) . "\n";
echo "   Average per Referral: ₹" . number_format($total_referral_earnings / count($target_user['promotion_report']), 2) . "\n\n";

// Count referred users
$referred_users_count = 0;
foreach ($users as $user_id => $user) {
    if (($user['referred_by'] ?? '') === $target_user_id) {
        $referred_users_count++;
    }
}

echo "👥 REFERRED USERS:\n";
echo "   Total Users Referred: $referred_users_count\n";
echo "   Referrals in promotion_report: " . count($target_user['promotion_report']) . "\n";
echo "   Data Consistency: " . ($referred_users_count === count($target_user['promotion_report']) ? "✅ Perfect Match" : "⚠️ Mismatch") . "\n\n";

// Analyze referral dates
$referral_dates = array_column($target_user['promotion_report'], 'created_at');
$earliest_date = min($referral_dates);
$latest_date = max($referral_dates);

echo "📅 DATE ANALYSIS:\n";
echo "   Earliest Referral: $earliest_date\n";
echo "   Latest Referral: $latest_date\n";
echo "   Date Range: " . round((strtotime($latest_date) - strtotime($earliest_date)) / (24 * 60 * 60)) . " days\n\n";

// Monthly breakdown
$monthly_breakdown = [];
foreach ($target_user['promotion_report'] as $referral) {
    $month = date('Y-m', strtotime($referral['created_at']));
    $monthly_breakdown[$month] = ($monthly_breakdown[$month] ?? 0) + 1;
}

echo "📊 MONTHLY BREAKDOWN:\n";
foreach ($monthly_breakdown as $month => $count) {
    echo "   " . date('M Y', strtotime($month . '-01')) . ": $count referrals\n";
}
echo "\n";

// Bonus distribution
$bonus_distribution = [];
foreach ($target_user['promotion_report'] as $referral) {
    $amount = $referral['amount_got'];
    $bonus_distribution[$amount] = ($bonus_distribution[$amount] ?? 0) + 1;
}

echo "💵 BONUS DISTRIBUTION:\n";
ksort($bonus_distribution);
foreach ($bonus_distribution as $amount => $count) {
    echo "   ₹$amount: $count referrals (" . round(($count / count($target_user['promotion_report'])) * 100, 1) . "%)\n";
}
echo "\n";

// Sample fake users
echo "🔍 SAMPLE FAKE USERS:\n";
$sample_count = 0;
foreach ($users as $user_id => $user) {
    if (($user['referred_by'] ?? '') === $target_user_id && $sample_count < 10) {
        echo "   $user_id: {$user['first_name']} (@{$user['username']}) - ₹" . number_format($user['balance'], 2) . "\n";
        $sample_count++;
    }
}
echo "   ... and " . ($referred_users_count - 10) . " more users\n\n";

// Quality checks
echo "✅ QUALITY VERIFICATION:\n";

// Check for realistic names
$realistic_names = ['Aarav', 'Vivaan', 'Aditya', 'Ananya', 'Diya', 'Priya', 'Rohit', 'Amit', 'Sunita', 'Rekha'];
$name_quality = 0;
foreach ($target_user['promotion_report'] as $referral) {
    foreach ($realistic_names as $name) {
        if (strpos($referral['referred_user_name'], $name) !== false) {
            $name_quality++;
            break;
        }
    }
}
echo "   Name Quality: " . round(($name_quality / count($target_user['promotion_report'])) * 100, 1) . "% realistic Indian names\n";

// Check date distribution (should not be clustered)
$date_clusters = [];
foreach ($referral_dates as $date) {
    $day = date('Y-m-d', strtotime($date));
    $date_clusters[$day] = ($date_clusters[$day] ?? 0) + 1;
}
$max_per_day = max($date_clusters);
echo "   Date Distribution: Max $max_per_day referrals per day (good if < 10)\n";

// Check balance distribution
$balance_ranges = [
    '0-50' => 0, '51-200' => 0, '201-500' => 0, '501-1000' => 0, '1000+' => 0
];
foreach ($users as $user_id => $user) {
    if (($user['referred_by'] ?? '') === $target_user_id) {
        $balance = $user['balance'] ?? 0;
        if ($balance <= 50) $balance_ranges['0-50']++;
        elseif ($balance <= 200) $balance_ranges['51-200']++;
        elseif ($balance <= 500) $balance_ranges['201-500']++;
        elseif ($balance <= 1000) $balance_ranges['501-1000']++;
        else $balance_ranges['1000+']++;
    }
}
echo "   Balance Distribution:\n";
foreach ($balance_ranges as $range => $count) {
    echo "     ₹$range: $count users (" . round(($count / $referred_users_count) * 100, 1) . "%)\n";
}

echo "\n🎯 ADMIN PANEL TESTING:\n";
echo "   1. Visit: admin/user_management.php\n";
echo "   2. Search for user: $target_user_id\n";
echo "   3. Click 'View' to see user profile\n";
echo "   4. Check referral analytics: admin/referral_analytics.php\n";
echo "   5. View referral chains: admin/referral_chains.php?user_id=$target_user_id\n";
echo "   6. Check financial analytics: admin/financial_analytics.php\n\n";

echo "✨ SUCCESS! The fake data looks completely realistic and natural!\n";
echo "   User $target_user_id (Devendra Yadav) now has 200+ referrals\n";
echo "   All data is properly integrated and will display naturally in the admin panel\n";
?>
