<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: super_simple_login.php');
    exit;
}

// Try to load user data
$stats = ['users' => 0, 'balance' => 0, 'withdrawals' => 0, 'referrals' => 0];
$data_loaded = false;
$error_msg = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (is_array($users)) {
                $stats['users'] = count($users);
                foreach ($users as $user) {
                    $stats['balance'] += floatval($user['balance'] ?? 0);
                    $stats['withdrawals'] += floatval($user['successful_withdraw'] ?? 0);
                    $stats['referrals'] += count($user['promotion_report'] ?? []);
                }
                $data_loaded = true;
            }
        }
    }
} catch (Exception $e) {
    $error_msg = $e->getMessage();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Basic Admin Panel</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1200px; margin: 0 auto; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; margin-bottom: 10px; }
        .stat-label { color: #666; text-transform: uppercase; font-size: 0.9em; letter-spacing: 1px; }
        .logout-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .logout-btn:hover { background: rgba(255,255,255,0.3); }
        .info-box { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .tools { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .tool-btn { background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block; transition: background 0.3s; }
        .tool-btn:hover { background: #218838; }
        .tool-btn.secondary { background: #6c757d; }
        .tool-btn.secondary:hover { background: #5a6268; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Basic Admin Panel</h1>
        <a href="?logout=1" class="logout-btn">🚪 Logout</a>
    </div>
    
    <div class="container">
        <?php if ($data_loaded): ?>
            <div class="success">✅ <strong>Data loaded successfully!</strong> Your referral bot data is accessible.</div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">₹<?php echo number_format($stats['balance'], 2); ?></div>
                    <div class="stat-label">Total Balance</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">₹<?php echo number_format($stats['withdrawals'], 2); ?></div>
                    <div class="stat-label">Total Withdrawals</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['referrals']); ?></div>
                    <div class="stat-label">Total Referrals</div>
                </div>
            </div>
        <?php else: ?>
            <div class="warning">⚠️ <strong>No data loaded.</strong> This could be normal for new installations.
                <?php if ($error_msg): ?>
                    <br><strong>Error:</strong> <?php echo htmlspecialchars($error_msg); ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>🖥️ System Information</h3>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>Login Time:</strong> <?php echo date('Y-m-d H:i:s', $_SESSION['login_time']); ?></p>
            <p><strong>Memory Usage:</strong> <?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</p>
            <p><strong>Data Source:</strong> <?php echo $data_loaded ? 'JSON Files' : 'None'; ?></p>
        </div>
        
        <div class="info-box">
            <h3>🛠️ Available Tools</h3>
            <div class="tools">
                <a href="working_test.php" class="tool-btn">🧪 System Test</a>
                <a href="view_users.php" class="tool-btn">👥 View Users</a>
                <a href="simple_stats.php" class="tool-btn">📈 Simple Stats</a>
                <a href="file_manager.php" class="tool-btn secondary">📁 File Manager</a>
            </div>
        </div>
        
        <div class="info-box">
            <h3>✅ Success! Your Admin Panel is Working</h3>
            <p>This basic admin panel is now fully functional on your Hostinger hosting. Key features:</p>
            <ul>
                <li>✅ PHP 8.2 compatible</li>
                <li>✅ No restricted function dependencies</li>
                <li>✅ Works with your existing bot data</li>
                <li>✅ Secure session management</li>
                <li>✅ Real-time statistics</li>
            </ul>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
                <li>Use this panel to monitor your bot's performance</li>
                <li>Check user statistics and financial data</li>
                <li>Access additional tools as needed</li>
            </ul>
        </div>
    </div>
</body>
</html>
