<?php
/**
 * Admin Dashboard - Main Statistics and Overview
 */

define('ADMIN_PANEL_ACCESS', true);
require_once 'config.php';
require_once 'auth.php';
require_once 'DataProcessor.php';

// Require authentication
AdminAuth::requireAuth();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'get_stats':
                $stats = DataProcessor::getUserStatistics();
                echo json_encode(['success' => true, 'data' => $stats]);
                break;
                
            case 'get_chart_data':
                $chartData = DataProcessor::getChartData();
                echo json_encode(['success' => true, 'data' => $chartData]);
                break;
                
            case 'get_leaderboards':
                $leaderboards = DataProcessor::getLeaderboards();
                echo json_encode(['success' => true, 'data' => $leaderboards]);
                break;
                
            case 'get_financial_analytics':
                $analytics = DataProcessor::getFinancialAnalytics();
                echo json_encode(['success' => true, 'data' => $analytics]);
                break;
                
            case 'refresh_cache':
                if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Invalid CSRF token');
                }
                
                DataProcessor::clearCache();
                logAdminAction('CACHE_REFRESH', 'Admin manually refreshed cache');
                echo json_encode(['success' => true, 'message' => 'Cache refreshed successfully']);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get initial data for page load
$stats = DataProcessor::getUserStatistics();
$chartData = DataProcessor::getChartData();
$leaderboards = DataProcessor::getLeaderboards();
$financialAnalytics = DataProcessor::getFinancialAnalytics();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ADMIN_PANEL_TITLE; ?> - Dashboard</title>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .leaderboard-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .leaderboard-item:hover {
            transform: translateX(4px);
        }
        
        .session-timer {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 4px 12px;
            font-size: 0.875rem;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .refresh-btn {
            transition: transform 0.2s;
        }
        
        .refresh-btn:hover {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="bi bi-speedometer2 me-2"></i>
                <?php echo ADMIN_PANEL_TITLE; ?>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <span class="session-timer me-3" id="sessionTimer">
                    <i class="bi bi-clock me-1"></i>
                    <span id="timeRemaining">--:--</span>
                </span>
                
                <button class="btn btn-outline-light btn-sm me-2 refresh-btn" onclick="refreshCache()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        Admin
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="users.php"><i class="bi bi-people me-2"></i>User Management</a></li>
                        <li><a class="dropdown-item" href="financial.php"><i class="bi bi-graph-up me-2"></i>Financial Analytics</a></li>
                        <li><a class="dropdown-item" href="analytics.php"><i class="bi bi-trophy me-2"></i>Analytics & Leaderboards</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon bg-primary me-3">
                            <i class="bi bi-people"></i>
                        </div>
                        <div>
                            <h6 class="card-title text-muted mb-1">Total Users</h6>
                            <h3 class="mb-0" id="totalUsers"><?php echo formatNumber($stats['total_users']); ?></h3>
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i>
                                <?php echo formatNumber($stats['new_users_today']); ?> today
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon bg-success me-3">
                            <i class="bi bi-currency-rupee"></i>
                        </div>
                        <div>
                            <h6 class="card-title text-muted mb-1">Total Withdrawals</h6>
                            <h3 class="mb-0" id="totalWithdrawals"><?php echo formatCurrency($stats['total_withdrawals']); ?></h3>
                            <small class="text-info">
                                <i class="bi bi-hourglass-split"></i>
                                <?php echo formatCurrency($stats['pending_withdrawals']); ?> pending
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon bg-info me-3">
                            <i class="bi bi-wallet2"></i>
                        </div>
                        <div>
                            <h6 class="card-title text-muted mb-1">Total User Balances</h6>
                            <h3 class="mb-0" id="totalBalance"><?php echo formatCurrency($stats['total_balance']); ?></h3>
                            <small class="text-muted">
                                Avg: <?php echo formatCurrency($stats['total_users'] > 0 ? $stats['total_balance'] / $stats['total_users'] : 0); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon bg-warning me-3">
                            <i class="bi bi-share"></i>
                        </div>
                        <div>
                            <h6 class="card-title text-muted mb-1">Total Referrals</h6>
                            <h3 class="mb-0" id="totalReferrals"><?php echo formatNumber($stats['total_referrals']); ?></h3>
                            <small class="text-success">
                                <?php echo formatNumber($stats['referred_users']); ?> referred users
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-graph-up me-2"></i>
                        User Registration Trend (Last 30 Days)
                    </h5>
                    <canvas id="registrationChart" height="100"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-pie-chart me-2"></i>
                        User Activity Distribution
                    </h5>
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Financial Overview Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart me-2"></i>
                        Financial Overview
                    </h5>
                    <canvas id="financialChart" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- Leaderboards -->
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-trophy me-2"></i>
                        Top Referrers
                    </h5>
                    <div id="topReferrers">
                        <?php foreach (array_slice($leaderboards['top_referrers'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item d-flex align-items-center">
                            <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                            <div class="flex-grow-1">
                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <br>
                                <small class="text-success"><?php echo $user['referrals']; ?> referrals • <?php echo formatCurrency($user['earnings']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-cash-stack me-2"></i>
                        Top Withdrawers
                    </h5>
                    <div id="topWithdrawers">
                        <?php foreach (array_slice($leaderboards['top_withdrawers'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item d-flex align-items-center">
                            <span class="badge bg-success me-2"><?php echo $index + 1; ?></span>
                            <div class="flex-grow-1">
                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <br>
                                <small class="text-success"><?php echo formatCurrency($user['amount']); ?> • <?php echo $user['withdrawal_count']; ?> withdrawals</small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-wallet me-2"></i>
                        Top Balances
                    </h5>
                    <div id="topBalances">
                        <?php foreach (array_slice($leaderboards['top_balances'], 0, 10) as $index => $user): ?>
                        <div class="leaderboard-item d-flex align-items-center">
                            <span class="badge bg-info me-2"><?php echo $index + 1; ?></span>
                            <div class="flex-grow-1">
                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                <?php if ($user['username']): ?>
                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                <?php endif; ?>
                                <br>
                                <small class="text-info"><?php echo formatCurrency($user['balance']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-spinner position-fixed top-50 start-50 translate-middle" style="z-index: 9999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let charts = {};
        let sessionTimer;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            startSessionTimer();
            
            // Auto-refresh every 5 minutes
            setInterval(refreshDashboard, 300000);
        });
        
        // Session timer
        function startSessionTimer() {
            updateSessionTimer();
            sessionTimer = setInterval(updateSessionTimer, 1000);
        }
        
        function updateSessionTimer() {
            fetch('auth.php?action=check_session')
                .then(response => response.json())
                .then(data => {
                    if (!data.authenticated) {
                        window.location.href = 'login.php';
                        return;
                    }
                    
                    const minutes = Math.floor(data.time_remaining / 60);
                    const seconds = data.time_remaining % 60;
                    document.getElementById('timeRemaining').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // Warning when less than 5 minutes
                    const timer = document.getElementById('sessionTimer');
                    if (data.time_remaining < 300) {
                        timer.classList.add('bg-warning');
                        timer.classList.remove('bg-transparent');
                    } else {
                        timer.classList.remove('bg-warning');
                        timer.classList.add('bg-transparent');
                    }
                });
        }
        
        // Initialize charts
        function initializeCharts() {
            const chartData = <?php echo json_encode($chartData); ?>;
            
            // Registration trend chart
            const regCtx = document.getElementById('registrationChart').getContext('2d');
            charts.registration = new Chart(regCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(chartData.registration_trend),
                    datasets: [{
                        label: 'New Users',
                        data: Object.values(chartData.registration_trend),
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Activity distribution chart
            const actCtx = document.getElementById('activityChart').getContext('2d');
            charts.activity = new Chart(actCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(chartData.activity_distribution),
                    datasets: [{
                        data: Object.values(chartData.activity_distribution),
                        backgroundColor: [
                            'rgb(5, 150, 105)',
                            'rgb(220, 38, 38)',
                            'rgb(100, 116, 139)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Financial overview chart
            const finCtx = document.getElementById('financialChart').getContext('2d');
            charts.financial = new Chart(finCtx, {
                type: 'bar',
                data: {
                    labels: Object.keys(chartData.financial_overview),
                    datasets: [{
                        label: 'Amount (₹)',
                        data: Object.values(chartData.financial_overview),
                        backgroundColor: [
                            'rgba(8, 145, 178, 0.8)',
                            'rgba(5, 150, 105, 0.8)',
                            'rgba(217, 119, 6, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // Refresh dashboard data
        function refreshDashboard() {
            showLoading(true);
            
            Promise.all([
                fetch('dashboard.php?action=get_stats').then(r => r.json()),
                fetch('dashboard.php?action=get_chart_data').then(r => r.json()),
                fetch('dashboard.php?action=get_leaderboards').then(r => r.json())
            ]).then(([stats, chartData, leaderboards]) => {
                if (stats.success) updateStats(stats.data);
                if (chartData.success) updateCharts(chartData.data);
                if (leaderboards.success) updateLeaderboards(leaderboards.data);
                
                showLoading(false);
            }).catch(error => {
                console.error('Error refreshing dashboard:', error);
                showLoading(false);
            });
        }
        
        // Update statistics
        function updateStats(stats) {
            document.getElementById('totalUsers').textContent = formatNumber(stats.total_users);
            document.getElementById('totalWithdrawals').textContent = formatCurrency(stats.total_withdrawals);
            document.getElementById('totalBalance').textContent = formatCurrency(stats.total_balance);
            document.getElementById('totalReferrals').textContent = formatNumber(stats.total_referrals);
        }
        
        // Update charts
        function updateCharts(data) {
            // Update registration chart
            charts.registration.data.labels = Object.keys(data.registration_trend);
            charts.registration.data.datasets[0].data = Object.values(data.registration_trend);
            charts.registration.update();
            
            // Update activity chart
            charts.activity.data.datasets[0].data = Object.values(data.activity_distribution);
            charts.activity.update();
            
            // Update financial chart
            charts.financial.data.datasets[0].data = Object.values(data.financial_overview);
            charts.financial.update();
        }
        
        // Update leaderboards
        function updateLeaderboards(data) {
            // Implementation for updating leaderboard sections
            // This would update the HTML content dynamically
        }
        
        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }
        
        function formatCurrency(amount) {
            return '₹' + amount.toLocaleString('en-IN', {minimumFractionDigits: 2});
        }
        
        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            spinner.style.display = show ? 'block' : 'none';
        }
        
        // Refresh cache
        function refreshCache() {
            const csrfToken = '<?php echo generateCSRFToken(); ?>';
            
            showLoading(true);
            
            fetch('dashboard.php?action=refresh_cache', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'csrf_token=' + encodeURIComponent(csrfToken)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshDashboard();
                } else {
                    alert('Error refreshing cache: ' + data.error);
                    showLoading(false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error refreshing cache');
                showLoading(false);
            });
        }
        
        // Logout
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('auth.php?action=logout')
                    .then(() => {
                        window.location.href = 'login.php';
                    });
            }
        }
    </script>
</body>
</html>
