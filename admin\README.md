# Referral Bot Admin Panel

A comprehensive PHP-based admin panel for managing Telegram referral bot systems, optimized for high-performance handling of 100,000+ users.

## Features

### 🎯 Core Features
- **Dashboard Overview**: Real-time statistics with interactive charts
- **User Management**: Advanced search, filtering, and pagination
- **Financial Management**: Revenue tracking and withdrawal management
- **Leaderboards**: Top referrers, withdrawers, and highest balances
- **Bot Statistics**: Comprehensive analytics and performance metrics
- **Settings Panel**: System configuration and cache management

### ⚡ Performance Optimizations
- **Database Indexing**: Optimized indexes for large datasets
- **Caching System**: Multi-level caching for improved response times
- **Pagination**: Efficient handling of large user lists
- **Query Optimization**: Optimized SQL queries for 100k+ users
- **Memory Management**: Optimized memory usage for shared hosting

### 🔒 Security Features
- **Passcode Authentication**: Secure access with configurable passcode
- **Session Management**: Auto-logout and session security
- **Data Protection**: Secure handling of sensitive user data
- **Input Validation**: Protection against common security threats

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7+ or MariaDB 10.2+ (for MySQL mode)
- Web server (Apache/Nginx)
- Minimum 256MB RAM (512MB recommended for large datasets)

### Quick Setup

1. **Upload Files**
   ```bash
   # Upload the entire /admin/ folder to your web server
   # Ensure proper file permissions (644 for files, 755 for directories)
   ```

2. **Configure Database** (MySQL Mode)
   ```bash
   # Run the database optimization script
   php admin/optimize_database.php
   ```

3. **Set Permissions**
   ```bash
   chmod 755 admin/
   chmod 644 admin/*.php
   chmod 755 admin/cache/
   chmod 755 admin/includes/
   ```

4. **Access Admin Panel**
   ```
   URL: https://yourdomain.com/admin/
   Passcode: 1412
   ```

## Configuration

### Storage Modes

The admin panel supports both storage modes:

**MySQL Mode** (Recommended for 100k+ users)
- High performance with optimized indexes
- Advanced analytics and reporting
- Better scalability

**JSON Mode** (Compatible with existing setup)
- Works with current JSON file storage
- No database setup required
- Suitable for smaller datasets

### Performance Settings

Key configuration options in `admin/config.php`:

```php
// Pagination settings
define('DEFAULT_PAGE_SIZE', 50);
define('MAX_PAGE_SIZE', 200);

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 300); // 5 minutes

// Performance settings
define('QUERY_TIMEOUT', 30);
define('MAX_MEMORY_LIMIT', '256M');
```

### Authentication

Default passcode: `1412`

To change the passcode, edit `admin/config.php`:
```php
define('ADMIN_PASSCODE', 'your_new_passcode');
```

## Usage Guide

### Dashboard
- View real-time statistics and key metrics
- Interactive charts for user activity and financial data
- Quick access to main functions

### User Management
- Search users by name, username, or ID
- Filter by status (active, banned, high balance, etc.)
- Pagination for efficient browsing
- Click on users for detailed information

### Financial Management
- Track total revenue and withdrawals
- Monitor pending withdrawal requests
- View financial analytics and trends
- Calculate commission earnings

### Leaderboards
- Top referrers with most successful referrals
- Top withdrawers by withdrawal amount
- Users with highest account balances
- Ranking system with visual indicators

### Bot Statistics
- User engagement metrics
- Growth trends and analytics
- Performance monitoring
- Conversion rate tracking

## Performance Optimization

### For 100k+ Users

1. **Database Optimization**
   ```bash
   # Run the optimization script
   php admin/optimize_database.php
   ```

2. **Enable Caching**
   ```php
   define('CACHE_ENABLED', true);
   define('CACHE_DURATION', 300);
   ```

3. **Optimize Pagination**
   ```php
   // Use smaller page sizes for better performance
   define('DEFAULT_PAGE_SIZE', 25);
   ```

4. **Server Configuration**
   ```apache
   # .htaccess optimizations
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 month"
       ExpiresByType application/javascript "access plus 1 month"
   </IfModule>
   ```

### Shared Hosting Optimization

- Memory limit optimization
- Query timeout management
- Efficient file caching
- Minimal resource usage

## Troubleshooting

### Common Issues

**Slow Loading**
- Enable caching in config.php
- Run database optimization script
- Reduce page size for large datasets

**Memory Errors**
- Increase PHP memory limit
- Optimize pagination settings
- Clear cache regularly

**Database Connection Issues**
- Verify database credentials in config.php
- Check database server status
- Ensure proper permissions

**Authentication Problems**
- Verify passcode in config.php
- Clear browser cache and cookies
- Check session configuration

### Performance Monitoring

Monitor these metrics for optimal performance:
- Page load times (should be < 3 seconds)
- Memory usage (should be < 80% of limit)
- Database query times
- Cache hit rates

## Security Best Practices

1. **Change Default Passcode**
   ```php
   define('ADMIN_PASSCODE', 'your_secure_passcode');
   ```

2. **Secure File Permissions**
   ```bash
   chmod 644 admin/*.php
   chmod 600 admin/config.php
   ```

3. **Regular Updates**
   - Keep PHP updated
   - Monitor security logs
   - Regular cache clearing

4. **Access Control**
   - Use HTTPS only
   - Implement IP restrictions if needed
   - Regular session timeout

## File Structure

```
admin/
├── config.php              # Main configuration
├── login.php               # Authentication
├── dashboard.php           # Main dashboard
├── users.php               # User management
├── finance.php             # Financial management
├── leaderboards.php        # Leaderboards
├── statistics.php          # Bot statistics
├── settings.php            # Settings panel
├── data_access.php         # Data layer
├── optimize_database.php   # DB optimization
├── includes/
│   ├── header.php          # Common header
│   └── footer.php          # Common footer
├── cache/                  # Cache directory
└── README.md               # This file
```

## Support

For technical support or questions:
- Check the troubleshooting section
- Review configuration settings
- Ensure all prerequisites are met
- Verify file permissions and database connectivity

## Version History

**v1.0.0** - Initial Release
- Complete admin panel implementation
- Performance optimizations for 100k+ users
- Comprehensive analytics and reporting
- Security features and authentication

---

**Note**: This admin panel is designed to work with the existing referral bot system and provides a powerful interface for managing large-scale bot operations efficiently.
