<?php
$pageTitle = 'Dashboard Overview';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();
$stats = $dataAccess->getDashboardStats();
$financialStats = $dataAccess->getFinancialStats();

// Calculate some additional metrics
$conversionRate = $stats['total_users'] > 0 ? ($stats['total_referrals'] / $stats['total_users']) * 100 : 0;
$avgBalancePerUser = $stats['total_users'] > 0 ? $stats['total_user_balance'] / $stats['total_users'] : 0;
$withdrawalRate = $stats['total_user_balance'] > 0 ? ($stats['total_withdrawals'] / ($stats['total_user_balance'] + $stats['total_withdrawals'])) * 100 : 0;
?>

<!-- Dashboard Stats Cards -->
<div class="row g-4 mb-4">
    <!-- Total Users -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <i class="fas fa-users"></i>
            </div>
            <div class="value"><?php echo formatNumber($stats['total_users']); ?></div>
            <div class="label">Total Users</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +<?php echo formatNumber($stats['new_users_today']); ?> today
            </div>
        </div>
    </div>
    
    <!-- Total Withdrawals -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="value"><?php echo formatCurrency($stats['total_withdrawals']); ?></div>
            <div class="label">Total Withdrawals</div>
            <div class="change positive">
                <i class="fas fa-percentage me-1"></i>
                <?php echo number_format($withdrawalRate, 1); ?>% rate
            </div>
        </div>
    </div>
    
    <!-- Pending Withdrawals -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-clock"></i>
            </div>
            <div class="value"><?php echo formatCurrency($stats['pending_withdrawals']); ?></div>
            <div class="label">Pending Withdrawals</div>
            <div class="change">
                <i class="fas fa-list me-1"></i>
                <?php echo formatNumber($stats['pending_withdrawal_requests'] ?? 0); ?> requests
            </div>
        </div>
    </div>
    
    <!-- Total User Balances -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white;">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="value"><?php echo formatCurrency($stats['total_user_balance']); ?></div>
            <div class="label">Total User Balances</div>
            <div class="change">
                <i class="fas fa-user me-1"></i>
                <?php echo formatCurrency($avgBalancePerUser); ?> avg
            </div>
        </div>
    </div>
    
    <!-- Active Users (7 days) -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="value"><?php echo formatNumber($stats['active_users_7d']); ?></div>
            <div class="label">Active Users (7 days)</div>
            <div class="change">
                <i class="fas fa-calendar me-1"></i>
                <?php echo formatNumber($stats['active_users_30d']); ?> (30 days)
            </div>
        </div>
    </div>
    
    <!-- Total Referrals -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                <i class="fas fa-share-alt"></i>
            </div>
            <div class="value"><?php echo formatNumber($stats['total_referrals']); ?></div>
            <div class="label">Total Referrals</div>
            <div class="change positive">
                <i class="fas fa-percentage me-1"></i>
                <?php echo number_format($conversionRate, 1); ?>% conversion
            </div>
        </div>
    </div>
    
    <!-- Banned Users -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-user-slash"></i>
            </div>
            <div class="value"><?php echo formatNumber($stats['banned_users']); ?></div>
            <div class="label">Banned Users</div>
            <div class="change">
                <i class="fas fa-percentage me-1"></i>
                <?php echo $stats['total_users'] > 0 ? number_format(($stats['banned_users'] / $stats['total_users']) * 100, 1) : 0; ?>% of total
            </div>
        </div>
    </div>
    
    <!-- New Users Today -->
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); color: white;">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="value"><?php echo formatNumber($stats['new_users_today']); ?></div>
            <div class="label">New Users Today</div>
            <div class="change positive">
                <i class="fas fa-chart-line me-1"></i>
                Growth trend
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mb-4">
    <!-- User Activity Distribution -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-pie me-2 text-primary"></i>
                User Activity Distribution
            </h5>
            <div class="chart-container">
                <canvas id="activityChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Financial Overview -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-doughnut me-2 text-success"></i>
                Financial Overview
            </h5>
            <div class="chart-container">
                <canvas id="financialChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Activity Distribution Bar Chart -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-bar me-2 text-info"></i>
                User Engagement Metrics
            </h5>
            <div class="chart-container">
                <canvas id="engagementChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4">
    <div class="col-lg-8">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-bolt me-2 text-warning"></i>
                Quick Actions
            </h5>
            <div class="row g-3">
                <div class="col-md-4">
                    <a href="users.php" class="btn btn-outline-primary w-100">
                        <i class="fas fa-users me-2"></i>
                        Manage Users
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="finance.php" class="btn btn-outline-success w-100">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        View Finances
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="leaderboards.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-trophy me-2"></i>
                        Leaderboards
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2 text-info"></i>
                System Status
            </h5>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>Storage Mode:</span>
                <span class="badge bg-primary"><?php echo strtoupper(STORAGE_MODE); ?></span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>Cache Status:</span>
                <span class="badge bg-success">
                    <i class="fas fa-check me-1"></i>Active
                </span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>Performance:</span>
                <span class="badge bg-success">Optimized</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <span>Last Updated:</span>
                <span class="text-muted small"><?php echo date('H:i:s'); ?></span>
            </div>
        </div>
    </div>
</div>

<?php
$pageScript = "
// User Activity Distribution Chart
const activityCtx = document.getElementById('activityChart').getContext('2d');
new Chart(activityCtx, {
    type: 'doughnut',
    data: {
        labels: ['Active Users', 'Banned Users', 'Inactive Users'],
        datasets: [{
            data: [
                " . ($stats['total_users'] - $stats['banned_users']) . ",
                " . $stats['banned_users'] . ",
                " . max(0, $stats['total_users'] - $stats['active_users_7d']) . "
            ],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#6c757d'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Financial Overview Chart
const financialCtx = document.getElementById('financialChart').getContext('2d');
new Chart(financialCtx, {
    type: 'pie',
    data: {
        labels: ['User Balances', 'Completed Withdrawals', 'Pending Withdrawals'],
        datasets: [{
            data: [
                " . $stats['total_user_balance'] . ",
                " . $stats['total_withdrawals'] . ",
                " . $stats['pending_withdrawals'] . "
            ],
            backgroundColor: [
                '#17a2b8',
                '#28a745',
                '#ffc107'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ₹' + new Intl.NumberFormat('en-IN').format(context.raw);
                    }
                }
            }
        }
    }
});

// User Engagement Metrics Chart
const engagementCtx = document.getElementById('engagementChart').getContext('2d');
new Chart(engagementCtx, {
    type: 'bar',
    data: {
        labels: ['Total Users', 'Active (7d)', 'Active (30d)', 'With Referrals', 'With Balance', 'Banned'],
        datasets: [{
            label: 'Count',
            data: [
                " . $stats['total_users'] . ",
                " . $stats['active_users_7d'] . ",
                " . $stats['active_users_30d'] . ",
                " . ($stats['total_referrals'] > 0 ? min($stats['total_users'], $stats['total_referrals']) : 0) . ",
                " . ($stats['total_user_balance'] > 0 ? $stats['total_users'] : 0) . ",
                " . $stats['banned_users'] . "
            ],
            backgroundColor: [
                '#667eea',
                '#28a745',
                '#17a2b8',
                '#6f42c1',
                '#ffc107',
                '#dc3545'
            ],
            borderRadius: 4,
            borderSkipped: false
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return AdminPanel.formatNumber(value);
                    }
                }
            }
        }
    }
});

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000);
";

require_once 'includes/footer.php';
?>
