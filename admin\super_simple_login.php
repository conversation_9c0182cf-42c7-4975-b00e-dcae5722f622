<?php
session_start();

// Check if already logged in
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: basic_admin.php');
    exit;
}

$error = '';

// Handle login
if ($_POST) {
    $passcode = $_POST['passcode'] ?? '';
    if ($passcode === '1412') {
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();
        header('Location: basic_admin.php');
        exit;
    } else {
        $error = 'Wrong passcode! Use: 1412';
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Super Simple Login</title>
    <style>
        body { font-family: Arial; background: #f0f0f0; padding: 50px; }
        .login-box { background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        input[type="password"] { width: 100%; padding: 15px; font-size: 16px; border: 2px solid #ddd; border-radius: 5px; margin: 10px 0; box-sizing: border-box; }
        button { width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="login-box">
        <h2>🔐 Super Simple Login</h2>
        <p>This is guaranteed to work on any hosting!</p>
        
        <?php if ($error): ?>
            <div class="error">❌ <?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="success">✅ PHP <?php echo PHP_VERSION; ?> is working!</div>
        
        <form method="POST">
            <label>Enter Passcode:</label>
            <input type="password" name="passcode" placeholder="Enter: 1412" required>
            <button type="submit">🚀 LOGIN</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px; color: #666;">
            <small>Passcode: <strong>1412</strong></small>
        </p>
    </div>
</body>
</html>
