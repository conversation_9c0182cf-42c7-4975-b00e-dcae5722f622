<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

$user_id = $_GET['id'] ?? '';
$user = null;
$users = [];
$error = '';

// Load user data
try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (isset($users[$user_id])) {
                $user = $users[$user_id];
                $user['user_id'] = $user_id;
            } else {
                $error = 'User not found';
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

if (!$user && !$error) {
    header('Location: user_management.php');
    exit;
}

// Calculate user statistics
$stats = [
    'total_referrals' => count($user['promotion_report'] ?? []),
    'total_referral_earnings' => array_sum(array_column($user['promotion_report'] ?? [], 'amount_got')),
    'account_age_days' => ceil((time() - strtotime($user['created_at'] ?? date('Y-m-d H:i:s'))) / (24 * 60 * 60)),
    'avg_referral_earnings' => 0,
    'last_activity' => $user['last_activity'] ?? $user['created_at'] ?? 'Unknown'
];

if ($stats['total_referrals'] > 0) {
    $stats['avg_referral_earnings'] = $stats['total_referral_earnings'] / $stats['total_referrals'];
}

// Get users referred by this user
$referred_users = [];
foreach ($users as $uid => $u) {
    if (($u['referred_by'] ?? '') === $user_id) {
        $referred_users[] = [
            'user_id' => $uid,
            'name' => $u['first_name'] ?? 'Unknown',
            'username' => $u['username'] ?? '',
            'balance' => $u['balance'] ?? 0,
            'created_at' => $u['created_at'] ?? '',
            'banned' => $u['banned'] ?? false
        ];
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>User Profile - <?php echo htmlspecialchars($user['first_name'] ?? 'Unknown'); ?></title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1200px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .user-header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .user-info { display: grid; grid-template-columns: auto 1fr auto; gap: 20px; align-items: center; }
        .user-avatar { width: 80px; height: 80px; background: linear-gradient(45deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2em; font-weight: bold; }
        .user-details h2 { margin: 0 0 10px 0; color: #333; }
        .user-meta { color: #666; font-size: 0.9em; }
        .user-status { text-align: right; }
        .status-badge { padding: 8px 16px; border-radius: 20px; font-size: 0.9em; font-weight: bold; }
        .status-active { background: #d4edda; color: #155724; }
        .status-banned { background: #f8d7da; color: #721c24; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-value { font-size: 1.8em; font-weight: bold; margin-bottom: 10px; }
        .stat-label { color: #666; font-size: 0.9em; text-transform: uppercase; letter-spacing: 1px; }
        .balance { color: #28a745; }
        .withdrawals { color: #17a2b8; }
        .referrals { color: #6f42c1; }
        .age { color: #ffc107; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .info-item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .info-item:last-child { border-bottom: none; }
        .info-label { color: #666; font-weight: bold; }
        .info-value { color: #333; }
        .referral-list { max-height: 400px; overflow-y: auto; }
        .referral-item { display: flex; justify-content: space-between; align-items: center; padding: 12px; border-bottom: 1px solid #eee; }
        .referral-item:hover { background: #f8f9fa; }
        .referral-info { flex: 1; }
        .referral-name { font-weight: bold; }
        .referral-details { font-size: 0.8em; color: #666; }
        .referral-value { text-align: right; }
        .actions { display: flex; gap: 10px; margin-top: 20px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; text-align: center; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.error { background: #f8d7da; color: #721c24; }
        .transaction-list { max-height: 300px; overflow-y: auto; }
        .transaction-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; font-size: 0.9em; }
        .transaction-item:hover { background: #f8f9fa; }
        .transaction-type { font-weight: bold; }
        .transaction-amount { font-weight: bold; }
        .transaction-date { color: #666; font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>👤 User Profile</h1>
        <a href="user_management.php" class="back-btn">← Back to Users</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php else: ?>
            
            <!-- User Header -->
            <div class="user-header">
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1)); ?>
                    </div>
                    <div class="user-details">
                        <h2><?php echo htmlspecialchars($user['first_name'] ?? 'Unknown User'); ?></h2>
                        <div class="user-meta">
                            <strong>ID:</strong> <?php echo htmlspecialchars($user_id); ?><br>
                            <?php if ($user['username'] ?? ''): ?>
                                <strong>Username:</strong> @<?php echo htmlspecialchars($user['username']); ?><br>
                            <?php endif; ?>
                            <strong>Joined:</strong> <?php echo date('M d, Y', strtotime($user['created_at'] ?? date('Y-m-d H:i:s'))); ?>
                            (<?php echo $stats['account_age_days']; ?> days ago)
                        </div>
                    </div>
                    <div class="user-status">
                        <?php if ($user['banned'] ?? false): ?>
                            <span class="status-badge status-banned">❌ Banned</span>
                        <?php else: ?>
                            <span class="status-badge status-active">✅ Active</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- User Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value balance">₹<?php echo number_format($user['balance'] ?? 0, 2); ?></div>
                    <div class="stat-label">Current Balance</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value withdrawals">₹<?php echo number_format($user['successful_withdraw'] ?? 0, 2); ?></div>
                    <div class="stat-label">Total Withdrawals</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value referrals"><?php echo $stats['total_referrals']; ?></div>
                    <div class="stat-label">Total Referrals</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value referrals">₹<?php echo number_format($stats['total_referral_earnings'], 2); ?></div>
                    <div class="stat-label">Referral Earnings</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value age"><?php echo $stats['account_age_days']; ?></div>
                    <div class="stat-label">Account Age (Days)</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value withdrawals">₹<?php echo number_format($user['withdraw_under_review'] ?? 0, 2); ?></div>
                    <div class="stat-label">Pending Withdrawals</div>
                </div>
            </div>
            
            <!-- Detailed Information -->
            <div class="info-grid">
                <div class="content-section">
                    <h3>📋 Account Information</h3>
                    <div class="info-item">
                        <span class="info-label">User ID:</span>
                        <span class="info-value"><?php echo htmlspecialchars($user_id); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Full Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($user['first_name'] ?? 'Not provided'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Username:</span>
                        <span class="info-value"><?php echo htmlspecialchars($user['username'] ?? 'Not set'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Registration Date:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($user['created_at'] ?? date('Y-m-d H:i:s'))); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Last Activity:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($stats['last_activity'])); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Account Status:</span>
                        <span class="info-value"><?php echo ($user['banned'] ?? false) ? 'Banned' : 'Active'; ?></span>
                    </div>
                </div>
                
                <div class="content-section">
                    <h3>💰 Financial Summary</h3>
                    <div class="info-item">
                        <span class="info-label">Current Balance:</span>
                        <span class="info-value">₹<?php echo number_format($user['balance'] ?? 0, 2); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total Withdrawals:</span>
                        <span class="info-value">₹<?php echo number_format($user['successful_withdraw'] ?? 0, 2); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Pending Withdrawals:</span>
                        <span class="info-value">₹<?php echo number_format($user['withdraw_under_review'] ?? 0, 2); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Joining Bonus:</span>
                        <span class="info-value">₹<?php echo number_format($user['joining_bonus_got'] ?? 0, 2); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Referral Earnings:</span>
                        <span class="info-value">₹<?php echo number_format($stats['total_referral_earnings'], 2); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Avg per Referral:</span>
                        <span class="info-value">₹<?php echo number_format($stats['avg_referral_earnings'], 2); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Users Referred by This User -->
            <?php if (!empty($referred_users)): ?>
                <div class="content-section">
                    <h3>👥 Users Referred by <?php echo htmlspecialchars($user['first_name'] ?? 'This User'); ?> (<?php echo count($referred_users); ?>)</h3>
                    <div class="referral-list">
                        <?php foreach ($referred_users as $referred): ?>
                            <div class="referral-item">
                                <div class="referral-info">
                                    <div class="referral-name"><?php echo htmlspecialchars($referred['name']); ?></div>
                                    <div class="referral-details">
                                        ID: <?php echo htmlspecialchars($referred['user_id']); ?>
                                        <?php if ($referred['username']): ?>
                                            • @<?php echo htmlspecialchars($referred['username']); ?>
                                        <?php endif; ?>
                                        • Joined: <?php echo date('M d, Y', strtotime($referred['created_at'])); ?>
                                        <?php if ($referred['banned']): ?>
                                            • <span style="color: #dc3545;">Banned</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="referral-value">
                                    <div style="font-weight: bold; color: #28a745;">₹<?php echo number_format($referred['balance'], 2); ?></div>
                                    <div style="font-size: 0.8em; color: #666;">Balance</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Referral Earnings History -->
            <?php if (!empty($user['promotion_report'])): ?>
                <div class="content-section">
                    <h3>💰 Referral Earnings History</h3>
                    <div class="transaction-list">
                        <?php foreach (array_reverse($user['promotion_report']) as $referral): ?>
                            <div class="transaction-item">
                                <div>
                                    <div class="transaction-type">Referral Commission</div>
                                    <div class="transaction-date">
                                        From: <?php echo htmlspecialchars($referral['referred_user_name'] ?? 'Unknown'); ?>
                                        • <?php echo date('M d, Y H:i', strtotime($referral['created_at'] ?? date('Y-m-d H:i:s'))); ?>
                                    </div>
                                </div>
                                <div class="transaction-amount" style="color: #28a745;">
                                    +₹<?php echo number_format($referral['amount_got'] ?? 0, 2); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Actions -->
            <div class="content-section">
                <h3>🛠️ User Actions</h3>
                <div class="actions">
                    <a href="referral_chains.php?user_id=<?php echo urlencode($user_id); ?>" class="btn">
                        🔗 View Referral Tree
                    </a>
                    
                    <a href="user_management.php?search=<?php echo urlencode($user_id); ?>" class="btn">
                        👥 Back to User List
                    </a>
                    
                    <a href="withdrawals.php" class="btn btn-success">
                        💰 Manage Withdrawals
                    </a>
                    
                    <a href="financial_analytics.php" class="btn btn-warning">
                        📊 Financial Analytics
                    </a>
                </div>
            </div>
            
        <?php endif; ?>
    </div>
</body>
</html>
