<?php
// Comprehensive Admin Panel Status Checker for Hostinger
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Status Check</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f6fa; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 10px; text-align: center; margin-bottom: 2rem; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .status-card { background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-card h3 { margin-top: 0; color: #333; border-bottom: 2px solid #f0f0f0; padding-bottom: 0.5rem; }
        .check-item { display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid #f0f0f0; }
        .check-item:last-child { border-bottom: none; }
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .overall-status { text-align: center; padding: 2rem; margin: 2rem 0; border-radius: 10px; font-size: 1.2rem; font-weight: bold; }
        .overall-good { background: #d4edda; color: #155724; border: 2px solid #28a745; }
        .overall-warning { background: #fff3cd; color: #856404; border: 2px solid #ffc107; }
        .overall-error { background: #f8d7da; color: #721c24; border: 2px solid #dc3545; }
        .action-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem; }
        .action-link { background: #667eea; color: white; padding: 1rem; text-decoration: none; border-radius: 8px; text-align: center; transition: background 0.3s; }
        .action-link:hover { background: #5a6fd8; }
        .details { font-size: 0.9rem; color: #666; margin-top: 0.5rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Admin Panel Status Check</h1>
            <p>Comprehensive status verification for Hostinger shared hosting</p>
        </div>
        
        <?php
        // Initialize status tracking
        $total_checks = 0;
        $passed_checks = 0;
        $warnings = 0;
        $errors = 0;
        
        // Helper function to add check result
        function addCheck($condition, $good_text, $bad_text, $is_warning = false) {
            global $total_checks, $passed_checks, $warnings, $errors;
            $total_checks++;
            
            if ($condition) {
                $passed_checks++;
                return "<span class='status-good'>✓ $good_text</span>";
            } else {
                if ($is_warning) {
                    $warnings++;
                    return "<span class='status-warning'>⚠ $bad_text</span>";
                } else {
                    $errors++;
                    return "<span class='status-error'>✗ $bad_text</span>";
                }
            }
        }
        ?>
        
        <div class="status-grid">
            <!-- Core System Check -->
            <div class="status-card">
                <h3>🖥️ Core System</h3>
                
                <div class="check-item">
                    <span>PHP Version</span>
                    <?php echo addCheck(version_compare(PHP_VERSION, '7.4.0', '>='), 
                        'PHP ' . PHP_VERSION . ' (Compatible)', 
                        'PHP ' . PHP_VERSION . ' (Requires 7.4+)'); ?>
                </div>
                
                <div class="check-item">
                    <span>Server Software</span>
                    <?php 
                    $server = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
                    echo addCheck(true, $server, '', true); 
                    ?>
                </div>
                
                <div class="check-item">
                    <span>Memory Limit</span>
                    <?php 
                    $memory = ini_get('memory_limit');
                    $sufficient = (int)$memory >= 128;
                    echo addCheck($sufficient, $memory . ' (Sufficient)', $memory . ' (May be low)', true); 
                    ?>
                </div>
                
                <div class="check-item">
                    <span>Session Support</span>
                    <?php echo addCheck(function_exists('session_start'), 'Available', 'Not available'); ?>
                </div>
            </div>
            
            <!-- File System Check -->
            <div class="status-card">
                <h3>📁 File System</h3>
                
                <div class="check-item">
                    <span>Admin Directory</span>
                    <?php echo addCheck(is_dir(__DIR__), 'Accessible', 'Not accessible'); ?>
                </div>
                
                <div class="check-item">
                    <span>Cache Directory</span>
                    <?php 
                    $cache_dir = __DIR__ . '/cache';
                    $cache_exists = is_dir($cache_dir);
                    $cache_writable = $cache_exists && is_writable($cache_dir);
                    echo addCheck($cache_writable, 'Exists and writable', 
                        $cache_exists ? 'Exists but not writable' : 'Missing', true); 
                    ?>
                </div>
                
                <div class="check-item">
                    <span>Config File</span>
                    <?php echo addCheck(file_exists(__DIR__ . '/config.php'), 'Found', 'Missing'); ?>
                </div>
                
                <div class="check-item">
                    <span>Data Directory</span>
                    <?php 
                    $data_dir = __DIR__ . '/../data/';
                    echo addCheck(is_dir($data_dir), 'Found', 'Missing', true); 
                    ?>
                </div>
            </div>
            
            <!-- Admin Panel Files -->
            <div class="status-card">
                <h3>📄 Admin Panel Files</h3>
                
                <?php
                $required_files = [
                    'login.php' => 'Login page',
                    'dashboard.php' => 'Main dashboard',
                    'users.php' => 'User management',
                    'data_access.php' => 'Data layer',
                    'includes/header.php' => 'Header template',
                    'includes/footer.php' => 'Footer template'
                ];
                
                foreach ($required_files as $file => $description): ?>
                    <div class="check-item">
                        <span><?php echo $description; ?></span>
                        <?php echo addCheck(file_exists(__DIR__ . '/' . $file), 'Found', 'Missing'); ?>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Configuration Test -->
            <div class="status-card">
                <h3>⚙️ Configuration</h3>
                
                <?php
                $config_loaded = false;
                $storage_mode = 'unknown';
                $admin_passcode_set = false;
                
                try {
                    if (file_exists(__DIR__ . '/config.php')) {
                        ob_start();
                        include_once __DIR__ . '/config.php';
                        ob_end_clean();
                        $config_loaded = true;
                        
                        if (defined('STORAGE_MODE')) {
                            $storage_mode = STORAGE_MODE;
                        }
                        
                        if (defined('ADMIN_PASSCODE')) {
                            $admin_passcode_set = true;
                        }
                    }
                } catch (Exception $e) {
                    // Config has issues
                }
                ?>
                
                <div class="check-item">
                    <span>Config Loading</span>
                    <?php echo addCheck($config_loaded, 'Loaded successfully', 'Failed to load'); ?>
                </div>
                
                <div class="check-item">
                    <span>Storage Mode</span>
                    <?php echo addCheck($storage_mode !== 'unknown', 
                        'Set to ' . strtoupper($storage_mode), 'Not configured'); ?>
                </div>
                
                <div class="check-item">
                    <span>Admin Passcode</span>
                    <?php echo addCheck($admin_passcode_set, 'Configured', 'Not set'); ?>
                </div>
                
                <div class="check-item">
                    <span>Cache Settings</span>
                    <?php echo addCheck(defined('CACHE_ENABLED'), 
                        CACHE_ENABLED ? 'Enabled' : 'Disabled', 'Not configured', true); ?>
                </div>
            </div>
            
            <!-- Data Access Test -->
            <div class="status-card">
                <h3>💾 Data Access</h3>
                
                <?php
                $data_accessible = false;
                $user_count = 0;
                $data_source = 'none';
                
                if ($storage_mode === 'json') {
                    $users_file = __DIR__ . '/../data/users.json';
                    if (file_exists($users_file)) {
                        try {
                            $users_content = file_get_contents($users_file);
                            $users_data = json_decode($users_content, true);
                            if (is_array($users_data)) {
                                $data_accessible = true;
                                $user_count = count($users_data);
                                $data_source = 'JSON';
                            }
                        } catch (Exception $e) {
                            // JSON read failed
                        }
                    }
                } elseif ($storage_mode === 'mysql') {
                    if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                        try {
                            $pdo = new PDO(
                                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                                DB_USER,
                                DB_PASS,
                                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_TIMEOUT => 5]
                            );
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                            if ($stmt) {
                                $result = $stmt->fetch();
                                $data_accessible = true;
                                $user_count = $result['count'];
                                $data_source = 'MySQL';
                            }
                        } catch (PDOException $e) {
                            // MySQL connection failed
                        }
                    }
                }
                ?>
                
                <div class="check-item">
                    <span>Data Source</span>
                    <?php echo addCheck($data_accessible, 
                        $data_source . ' accessible', 'No data source available', true); ?>
                </div>
                
                <div class="check-item">
                    <span>User Data</span>
                    <?php echo addCheck($data_accessible, 
                        $user_count . ' users found', 'No user data', true); ?>
                </div>
                
                <?php if ($storage_mode === 'mysql'): ?>
                    <div class="check-item">
                        <span>Database Connection</span>
                        <?php 
                        $db_connected = false;
                        if (defined('DB_HOST')) {
                            try {
                                $pdo = new PDO(
                                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                                    DB_USER,
                                    DB_PASS,
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_TIMEOUT => 5]
                                );
                                $db_connected = true;
                            } catch (PDOException $e) {
                                // Connection failed
                            }
                        }
                        echo addCheck($db_connected, 'Connected', 'Failed to connect', true);
                        ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Admin Panel Access -->
            <div class="status-card">
                <h3>🔐 Admin Panel Access</h3>
                
                <div class="check-item">
                    <span>Simple Login</span>
                    <?php echo addCheck(file_exists(__DIR__ . '/simple_login.php'), 
                        'Available', 'Missing'); ?>
                </div>
                
                <div class="check-item">
                    <span>Emergency Dashboard</span>
                    <?php echo addCheck(file_exists(__DIR__ . '/emergency_dashboard.php'), 
                        'Available', 'Missing'); ?>
                </div>
                
                <div class="check-item">
                    <span>Full Admin Panel</span>
                    <?php echo addCheck(file_exists(__DIR__ . '/login.php') && $config_loaded, 
                        'Ready', 'Not ready', true); ?>
                </div>
                
                <div class="check-item">
                    <span>Debug Tools</span>
                    <?php echo addCheck(file_exists(__DIR__ . '/debug.php'), 
                        'Available', 'Missing'); ?>
                </div>
            </div>
        </div>
        
        <!-- Overall Status -->
        <?php
        $success_rate = ($total_checks > 0) ? ($passed_checks / $total_checks) * 100 : 0;
        $overall_class = 'overall-error';
        $overall_message = 'Critical issues detected';
        $overall_icon = '❌';
        
        if ($success_rate >= 90) {
            $overall_class = 'overall-good';
            $overall_message = 'Admin panel is ready to use!';
            $overall_icon = '✅';
        } elseif ($success_rate >= 70) {
            $overall_class = 'overall-warning';
            $overall_message = 'Admin panel should work with minor limitations';
            $overall_icon = '⚠️';
        }
        ?>
        
        <div class="overall-status <?php echo $overall_class; ?>">
            <?php echo $overall_icon; ?> <strong><?php echo $overall_message; ?></strong><br>
            <div class="details">
                Status: <?php echo $passed_checks; ?>/<?php echo $total_checks; ?> checks passed 
                (<?php echo round($success_rate); ?>% success rate)
                <?php if ($warnings > 0): ?>
                    • <?php echo $warnings; ?> warnings
                <?php endif; ?>
                <?php if ($errors > 0): ?>
                    • <?php echo $errors; ?> errors
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Action Links -->
        <div class="action-links">
            <?php if ($success_rate >= 70): ?>
                <a href="simple_login.php" class="action-link">🚀 Access Simple Login</a>
                <a href="login.php" class="action-link">🎨 Access Full Admin Panel</a>
            <?php endif; ?>
            
            <a href="debug.php" class="action-link">🔍 Detailed Debug Info</a>
            <a href="hosting_check.php" class="action-link">🖥️ Hosting Compatibility</a>
            
            <?php if ($errors > 0 || $warnings > 0): ?>
                <a href="fix_permissions.php" class="action-link">🔧 Fix Permissions</a>
            <?php endif; ?>
            
            <a href="emergency_dashboard.php" class="action-link">🛡️ Emergency Dashboard</a>
        </div>
        
        <div style="text-align: center; margin-top: 2rem; color: #666; font-size: 0.9rem;">
            <p>Admin Panel Status Check • <?php echo date('Y-m-d H:i:s'); ?> • PHP <?php echo PHP_VERSION; ?></p>
        </div>
    </div>
</body>
</html>
