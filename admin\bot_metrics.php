<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load data
$users = [];
$error = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (!is_array($users)) {
                $users = [];
                $error = 'Invalid JSON format';
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Calculate bot metrics
$metrics = [
    'total_users' => count($users),
    'active_users' => 0,
    'new_users_today' => 0,
    'new_users_week' => 0,
    'new_users_month' => 0,
    'banned_users' => 0,
    'users_with_balance' => 0,
    'users_with_withdrawals' => 0,
    'avg_user_age_days' => 0,
    'retention_rate' => 0,
    'engagement_score' => 0
];

$today = date('Y-m-d');
$week_ago = date('Y-m-d', strtotime('-7 days'));
$month_ago = date('Y-m-d', strtotime('-30 days'));
$total_age_days = 0;

// User activity patterns (by hour)
$hourly_activity = array_fill(0, 24, 0);
$daily_activity = [];
$monthly_growth = [];

// Initialize daily activity for last 30 days
for ($i = 29; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $daily_activity[$date] = 0;
}

// Initialize monthly growth for last 12 months
for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $monthly_growth[$month] = [
        'month' => date('M Y', strtotime("-$i months")),
        'new_users' => 0,
        'total_users' => 0
    ];
}

// Process users
foreach ($users as $user_id => $user) {
    $created_at = $user['created_at'] ?? date('Y-m-d H:i:s');
    $created_date = date('Y-m-d', strtotime($created_at));
    $created_month = date('Y-m', strtotime($created_at));
    $created_hour = date('H', strtotime($created_at));
    
    // Count new users
    if ($created_date === $today) {
        $metrics['new_users_today']++;
    }
    if ($created_date >= $week_ago) {
        $metrics['new_users_week']++;
    }
    if ($created_date >= $month_ago) {
        $metrics['new_users_month']++;
    }
    
    // Activity patterns
    if (isset($daily_activity[$created_date])) {
        $daily_activity[$created_date]++;
    }
    
    if (isset($monthly_growth[$created_month])) {
        $monthly_growth[$created_month]['new_users']++;
    }
    
    $hourly_activity[intval($created_hour)]++;
    
    // User status
    if ($user['banned'] ?? false) {
        $metrics['banned_users']++;
    } else {
        $metrics['active_users']++;
    }
    
    if (($user['balance'] ?? 0) > 0) {
        $metrics['users_with_balance']++;
    }
    
    if (($user['successful_withdraw'] ?? 0) > 0) {
        $metrics['users_with_withdrawals']++;
    }
    
    // Calculate user age
    $user_age = (time() - strtotime($created_at)) / (24 * 60 * 60);
    $total_age_days += $user_age;
}

// Calculate averages and rates
if ($metrics['total_users'] > 0) {
    $metrics['avg_user_age_days'] = $total_age_days / $metrics['total_users'];
    $metrics['retention_rate'] = ($metrics['active_users'] / $metrics['total_users']) * 100;
    $metrics['engagement_score'] = (($metrics['users_with_balance'] + $metrics['users_with_withdrawals']) / $metrics['total_users']) * 100;
}

// Calculate cumulative totals for monthly growth
$cumulative_total = 0;
foreach ($monthly_growth as $month => &$data) {
    $cumulative_total += $data['new_users'];
    $data['total_users'] = $cumulative_total;
}

// Growth rate calculations
$growth_rates = [
    'daily' => 0,
    'weekly' => 0,
    'monthly' => 0
];

if ($metrics['total_users'] > $metrics['new_users_today']) {
    $yesterday_total = $metrics['total_users'] - $metrics['new_users_today'];
    $growth_rates['daily'] = $yesterday_total > 0 ? ($metrics['new_users_today'] / $yesterday_total) * 100 : 0;
}

if ($metrics['total_users'] > $metrics['new_users_week']) {
    $week_ago_total = $metrics['total_users'] - $metrics['new_users_week'];
    $growth_rates['weekly'] = $week_ago_total > 0 ? ($metrics['new_users_week'] / $week_ago_total) * 100 : 0;
}

if ($metrics['total_users'] > $metrics['new_users_month']) {
    $month_ago_total = $metrics['total_users'] - $metrics['new_users_month'];
    $growth_rates['monthly'] = $month_ago_total > 0 ? ($metrics['new_users_month'] / $month_ago_total) * 100 : 0;
}

// Peak activity analysis
$peak_hour = array_keys($hourly_activity, max($hourly_activity))[0];
$peak_day = array_keys($daily_activity, max($daily_activity))[0];

// User segments
$user_segments = [
    'new_users' => ['count' => 0, 'percentage' => 0],
    'active_users' => ['count' => 0, 'percentage' => 0],
    'power_users' => ['count' => 0, 'percentage' => 0],
    'inactive_users' => ['count' => 0, 'percentage' => 0]
];

foreach ($users as $user) {
    $created_days_ago = (time() - strtotime($user['created_at'] ?? date('Y-m-d H:i:s'))) / (24 * 60 * 60);
    $balance = $user['balance'] ?? 0;
    $referrals = count($user['promotion_report'] ?? []);
    $withdrawals = $user['successful_withdraw'] ?? 0;
    
    if ($created_days_ago <= 7) {
        $user_segments['new_users']['count']++;
    } elseif ($balance > 0 || $referrals > 0 || $withdrawals > 0) {
        if ($referrals >= 5 || $withdrawals > 1000 || $balance > 2000) {
            $user_segments['power_users']['count']++;
        } else {
            $user_segments['active_users']['count']++;
        }
    } else {
        $user_segments['inactive_users']['count']++;
    }
}

// Calculate percentages
foreach ($user_segments as $segment => &$data) {
    $data['percentage'] = $metrics['total_users'] > 0 ? ($data['count'] / $metrics['total_users']) * 100 : 0;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Bot Performance Metrics</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #17a2b8, #007bff); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 10px; }
        .metric-label { color: #666; font-size: 0.9em; text-transform: uppercase; letter-spacing: 1px; }
        .metric-change { font-size: 0.8em; margin-top: 5px; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        .users { color: #007bff; }
        .growth { color: #28a745; }
        .engagement { color: #6f42c1; }
        .activity { color: #17a2b8; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .charts-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .chart-placeholder { height: 250px; background: #f8f9fa; border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center; color: #6c757d; border-radius: 8px; flex-direction: column; }
        .activity-grid { display: grid; grid-template-columns: repeat(12, 1fr); gap: 5px; margin: 20px 0; }
        .activity-hour { background: #e9ecef; padding: 8px; text-align: center; border-radius: 4px; font-size: 0.8em; }
        .activity-hour.peak { background: #007bff; color: white; }
        .activity-hour.high { background: #17a2b8; color: white; }
        .activity-hour.medium { background: #6c757d; color: white; }
        .segments-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .segment-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .segment-card.new { border-left-color: #28a745; }
        .segment-card.active { border-left-color: #007bff; }
        .segment-card.power { border-left-color: #6f42c1; }
        .segment-card.inactive { border-left-color: #6c757d; }
        .segment-count { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .segment-percentage { color: #666; font-size: 0.9em; }
        .growth-indicators { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .growth-item { background: white; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #e9ecef; }
        .growth-value { font-size: 1.3em; font-weight: bold; margin-bottom: 5px; }
        .growth-label { color: #666; font-size: 0.8em; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.error { background: #f8d7da; color: #721c24; }
        .message.info { background: #d1ecf1; color: #0c5460; }
        .insights-list { list-style: none; padding: 0; }
        .insights-list li { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 Bot Performance Metrics</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Key Performance Metrics -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value users"><?php echo number_format($metrics['total_users']); ?></div>
                <div class="metric-label">Total Users</div>
                <div class="metric-change positive">+<?php echo number_format($metrics['new_users_today']); ?> today</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value growth"><?php echo number_format($metrics['new_users_month']); ?></div>
                <div class="metric-label">New Users (30d)</div>
                <div class="metric-change positive">+<?php echo number_format($growth_rates['monthly'], 1); ?>% growth</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value activity"><?php echo number_format($metrics['retention_rate'], 1); ?>%</div>
                <div class="metric-label">Retention Rate</div>
                <div class="metric-change positive"><?php echo number_format($metrics['active_users']); ?> active users</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value engagement"><?php echo number_format($metrics['engagement_score'], 1); ?>%</div>
                <div class="metric-label">Engagement Score</div>
                <div class="metric-change positive">Users with activity</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value neutral"><?php echo number_format($metrics['avg_user_age_days'], 1); ?></div>
                <div class="metric-label">Avg User Age (Days)</div>
                <div class="metric-change neutral">Since registration</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value users"><?php echo number_format($metrics['banned_users']); ?></div>
                <div class="metric-label">Banned Users</div>
                <div class="metric-change neutral"><?php echo number_format(($metrics['banned_users'] / max(1, $metrics['total_users'])) * 100, 1); ?>% of total</div>
            </div>
        </div>
        
        <!-- Growth Indicators -->
        <div class="content-section">
            <h3>📈 Growth Indicators</h3>
            <div class="growth-indicators">
                <div class="growth-item">
                    <div class="growth-value positive">+<?php echo number_format($metrics['new_users_today']); ?></div>
                    <div class="growth-label">Today</div>
                </div>
                <div class="growth-item">
                    <div class="growth-value positive">+<?php echo number_format($metrics['new_users_week']); ?></div>
                    <div class="growth-label">This Week</div>
                </div>
                <div class="growth-item">
                    <div class="growth-value positive">+<?php echo number_format($metrics['new_users_month']); ?></div>
                    <div class="growth-label">This Month</div>
                </div>
                <div class="growth-item">
                    <div class="growth-value growth">+<?php echo number_format($growth_rates['daily'], 1); ?>%</div>
                    <div class="growth-label">Daily Growth</div>
                </div>
                <div class="growth-item">
                    <div class="growth-value growth">+<?php echo number_format($growth_rates['weekly'], 1); ?>%</div>
                    <div class="growth-label">Weekly Growth</div>
                </div>
                <div class="growth-item">
                    <div class="growth-value growth">+<?php echo number_format($growth_rates['monthly'], 1); ?>%</div>
                    <div class="growth-label">Monthly Growth</div>
                </div>
            </div>
        </div>
        
        <!-- User Segments -->
        <div class="content-section">
            <h3>👥 User Segments</h3>
            <div class="segments-grid">
                <div class="segment-card new">
                    <div class="segment-count" style="color: #28a745;"><?php echo number_format($user_segments['new_users']['count']); ?></div>
                    <div class="segment-label">New Users</div>
                    <div class="segment-percentage"><?php echo number_format($user_segments['new_users']['percentage'], 1); ?>% of total</div>
                </div>
                
                <div class="segment-card active">
                    <div class="segment-count" style="color: #007bff;"><?php echo number_format($user_segments['active_users']['count']); ?></div>
                    <div class="segment-label">Active Users</div>
                    <div class="segment-percentage"><?php echo number_format($user_segments['active_users']['percentage'], 1); ?>% of total</div>
                </div>
                
                <div class="segment-card power">
                    <div class="segment-count" style="color: #6f42c1;"><?php echo number_format($user_segments['power_users']['count']); ?></div>
                    <div class="segment-label">Power Users</div>
                    <div class="segment-percentage"><?php echo number_format($user_segments['power_users']['percentage'], 1); ?>% of total</div>
                </div>
                
                <div class="segment-card inactive">
                    <div class="segment-count" style="color: #6c757d;"><?php echo number_format($user_segments['inactive_users']['count']); ?></div>
                    <div class="segment-label">Inactive Users</div>
                    <div class="segment-percentage"><?php echo number_format($user_segments['inactive_users']['percentage'], 1); ?>% of total</div>
                </div>
            </div>
        </div>
        
        <!-- Activity Patterns -->
        <div class="content-section">
            <h3>🕐 User Activity Patterns</h3>
            <h4>Registration Activity by Hour</h4>
            <div class="activity-grid">
                <?php for ($hour = 0; $hour < 24; $hour++): ?>
                    <?php
                    $activity_level = '';
                    $count = $hourly_activity[$hour];
                    $max_activity = max($hourly_activity);
                    
                    if ($count === $max_activity && $count > 0) {
                        $activity_level = 'peak';
                    } elseif ($count > $max_activity * 0.7) {
                        $activity_level = 'high';
                    } elseif ($count > $max_activity * 0.3) {
                        $activity_level = 'medium';
                    }
                    ?>
                    <div class="activity-hour <?php echo $activity_level; ?>" title="<?php echo $count; ?> users registered at <?php echo sprintf('%02d:00', $hour); ?>">
                        <?php echo sprintf('%02d:00', $hour); ?><br>
                        <strong><?php echo $count; ?></strong>
                    </div>
                <?php endfor; ?>
            </div>
            <p><strong>Peak Activity:</strong> <?php echo sprintf('%02d:00', $peak_hour); ?> with <?php echo $hourly_activity[$peak_hour]; ?> registrations</p>
        </div>
        
        <!-- Charts Section -->
        <div class="charts-grid">
            <div class="chart-container">
                <h3>📊 Monthly Growth Trend</h3>
                <div class="chart-placeholder">
                    <div style="text-align: center;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📈 Growth Chart</div>
                        <div>Monthly user acquisition:</div>
                        <ul style="text-align: left; margin-top: 10px;">
                            <?php foreach (array_slice($monthly_growth, -6) as $month_data): ?>
                                <li><?php echo $month_data['month']; ?>: +<?php echo $month_data['new_users']; ?> users</li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>📊 Daily Activity (Last 30 Days)</h3>
                <div class="chart-placeholder">
                    <div style="text-align: center;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📅 Daily Registrations</div>
                        <div>Recent activity:</div>
                        <ul style="text-align: left; margin-top: 10px;">
                            <li>Peak Day: <?php echo date('M d', strtotime($peak_day)); ?> (<?php echo max($daily_activity); ?> users)</li>
                            <li>Average: <?php echo number_format(array_sum($daily_activity) / count($daily_activity), 1); ?> users/day</li>
                            <li>Total (30d): <?php echo array_sum($daily_activity); ?> users</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Insights -->
        <div class="content-section">
            <h3>💡 Performance Insights</h3>
            <ul class="insights-list">
                <li>
                    <strong>User Growth:</strong> 
                    <?php if ($growth_rates['monthly'] > 10): ?>
                        Excellent growth rate of <?php echo number_format($growth_rates['monthly'], 1); ?>% this month
                    <?php elseif ($growth_rates['monthly'] > 5): ?>
                        Good growth rate of <?php echo number_format($growth_rates['monthly'], 1); ?>% this month
                    <?php else: ?>
                        Growth rate of <?php echo number_format($growth_rates['monthly'], 1); ?>% - consider marketing campaigns
                    <?php endif; ?>
                </li>
                
                <li>
                    <strong>User Engagement:</strong>
                    <?php if ($metrics['engagement_score'] > 70): ?>
                        High engagement with <?php echo number_format($metrics['engagement_score'], 1); ?>% of users showing activity
                    <?php elseif ($metrics['engagement_score'] > 40): ?>
                        Moderate engagement at <?php echo number_format($metrics['engagement_score'], 1); ?>%
                    <?php else: ?>
                        Low engagement at <?php echo number_format($metrics['engagement_score'], 1); ?>% - focus on user activation
                    <?php endif; ?>
                </li>
                
                <li>
                    <strong>Peak Activity:</strong> 
                    Most users register at <?php echo sprintf('%02d:00', $peak_hour); ?> - optimal time for promotions
                </li>
                
                <li>
                    <strong>User Retention:</strong>
                    <?php if ($metrics['retention_rate'] > 90): ?>
                        Excellent retention rate of <?php echo number_format($metrics['retention_rate'], 1); ?>%
                    <?php elseif ($metrics['retention_rate'] > 80): ?>
                        Good retention rate of <?php echo number_format($metrics['retention_rate'], 1); ?>%
                    <?php else: ?>
                        Retention rate of <?php echo number_format($metrics['retention_rate'], 1); ?>% - review user experience
                    <?php endif; ?>
                </li>
                
                <li>
                    <strong>Power Users:</strong>
                    <?php echo number_format($user_segments['power_users']['percentage'], 1); ?>% of users are highly engaged - focus on retention strategies
                </li>
            </ul>
        </div>
        
        <!-- Quick Actions -->
        <div class="content-section">
            <h3>⚡ Quick Actions</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="user_management.php?filter=active" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    👥 View Active Users
                </a>
                <a href="referral_analytics.php" style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    📈 Referral Analytics
                </a>
                <a href="financial_analytics.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    💰 Financial Analytics
                </a>
                <a href="system_admin.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    ⚙️ System Settings
                </a>
            </div>
        </div>
    </div>
</body>
</html>
