<?php
// Debug script to identify HTTP 500 errors (PHP 8.2 compatible)
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Shared hosting compatible syntax checker (no exec() required)
function checkPHPSyntax($filename) {
    if (!file_exists($filename)) {
        return "File not found";
    }

    // Check if exec is available (rare on shared hosting)
    if (function_exists('exec')) {
        $output = [];
        $return_var = 0;
        exec("php -l " . escapeshellarg($filename) . " 2>&1", $output, $return_var);

        if ($return_var === 0) {
            return true;
        } else {
            return implode("\n", $output);
        }
    }

    // Alternative method for shared hosting
    try {
        $content = file_get_contents($filename);
        if ($content === false) {
            return "Cannot read file";
        }

        // Try to tokenize the PHP code
        $tokens = @token_get_all($content);
        if ($tokens === false) {
            return "Tokenization failed - possible syntax error";
        }

        // For config files with includes, this is often a false positive
        if (strpos($filename, 'config.php') !== false) {
            return "Config file - syntax check skipped (includes/requires present)";
        }

        return true; // Basic syntax appears OK
    } catch (ParseError $e) {
        return "Parse error: " . $e->getMessage();
    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
}

echo "<h2>Admin Panel Debug Information</h2>";
echo "<hr>";

// Check PHP version
echo "<h3>1. PHP Environment</h3>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "<br>";

// Check file permissions
echo "<h3>2. File Permissions</h3>";
$files_to_check = [
    'config.php',
    'login.php', 
    'dashboard.php',
    'data_access.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $perms = substr(sprintf('%o', fileperms($filepath)), -4);
        echo "✓ $file: $perms<br>";
    } else {
        echo "✗ $file: <span style='color:red'>NOT FOUND</span><br>";
    }
}

// Check directory permissions
$dirs_to_check = ['cache', 'includes'];
foreach ($dirs_to_check as $dir) {
    $dirpath = __DIR__ . '/' . $dir;
    if (is_dir($dirpath)) {
        $perms = substr(sprintf('%o', fileperms($dirpath)), -4);
        $writable = is_writable($dirpath) ? 'WRITABLE' : 'NOT WRITABLE';
        echo "✓ $dir/: $perms ($writable)<br>";
    } else {
        echo "✗ $dir/: <span style='color:red'>NOT FOUND</span><br>";
    }
}
echo "<br>";

// Check required PHP extensions
echo "<h3>3. PHP Extensions</h3>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✓' : '✗';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<span style='color:$color'>$status $ext</span><br>";
}
echo "<br>";

// Check disabled functions (common on shared hosting)
echo "<h3>3.1. Shared Hosting Function Check</h3>";
$potentially_disabled = ['exec', 'shell_exec', 'system', 'passthru', 'proc_open', 'popen'];
foreach ($potentially_disabled as $func) {
    $available = function_exists($func);
    $status = $available ? '✓' : '✗';
    $color = $available ? 'green' : 'orange';
    $note = $available ? 'Available' : 'Disabled (normal on shared hosting)';
    echo "<span style='color:$color'>$status $func - $note</span><br>";
}
echo "<br>";

// Check memory and limits
echo "<h3>4. PHP Configuration</h3>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Error Reporting: " . ini_get('error_reporting') . "<br>";
echo "Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "<br>";
echo "<br>";

// Test basic file inclusion
echo "<h3>5. File Inclusion Test</h3>";
try {
    if (file_exists(__DIR__ . '/config.php')) {
        echo "✓ config.php exists<br>";
        
        // Try to include without executing
        $config_content = file_get_contents(__DIR__ . '/config.php');
        if ($config_content !== false) {
            echo "✓ config.php readable<br>";

            // Check for syntax errors (PHP 8.2 compatible)
            $syntax_check = checkPHPSyntax(__DIR__ . '/config.php');
            if ($syntax_check === true) {
                echo "✓ config.php syntax OK<br>";
            } else {
                echo "✗ <span style='color:red'>config.php has syntax errors: " . htmlspecialchars($syntax_check) . "</span><br>";
            }
        } else {
            echo "✗ <span style='color:red'>config.php not readable</span><br>";
        }
    } else {
        echo "✗ <span style='color:red'>config.php not found</span><br>";
    }
} catch (Exception $e) {
    echo "✗ <span style='color:red'>Error checking config.php: " . $e->getMessage() . "</span><br>";
}
echo "<br>";

// Test database connection (if config exists)
echo "<h3>6. Database Connection Test</h3>";
try {
    if (file_exists(__DIR__ . '/config.php')) {
        echo "✓ Config file found<br>";

        // Safely include config with error suppression
        $config_loaded = false;
        try {
            ob_start();
            $old_error_reporting = error_reporting(E_ERROR | E_PARSE); // Suppress warnings
            include_once __DIR__ . '/config.php';
            error_reporting($old_error_reporting);
            $config_output = ob_get_clean();
            $config_loaded = true;
            echo "✓ Config file loaded successfully<br>";
        } catch (Exception $e) {
            ob_end_clean();
            echo "⚠ <span style='color:orange'>Config loaded with warnings: " . $e->getMessage() . "</span><br>";
            $config_loaded = true; // Still try to continue
        }

        if ($config_loaded && defined('STORAGE_MODE')) {
            echo "✓ STORAGE_MODE: <strong>" . STORAGE_MODE . "</strong><br>";

            if (STORAGE_MODE === 'mysql') {
                echo "📊 Testing MySQL database connection...<br>";

                if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                    echo "✓ Database constants defined<br>";
                    echo "&nbsp;&nbsp;• Host: " . DB_HOST . "<br>";
                    echo "&nbsp;&nbsp;• Database: " . DB_NAME . "<br>";
                    echo "&nbsp;&nbsp;• User: " . DB_USER . "<br>";

                    try {
                        $pdo = new PDO(
                            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                            DB_USER,
                            DB_PASS,
                            [
                                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                                PDO::ATTR_TIMEOUT => 10
                            ]
                        );
                        echo "✓ <span style='color:green'><strong>Database connection successful!</strong></span><br>";

                        // Test a simple query
                        $stmt = $pdo->query("SELECT COUNT(*) as user_count FROM users");
                        if ($stmt) {
                            $result = $stmt->fetch();
                            echo "✓ <span style='color:green'>Database query test passed - Found " . $result['user_count'] . " users</span><br>";
                        }

                    } catch (PDOException $e) {
                        echo "✗ <span style='color:red'><strong>Database connection failed:</strong> " . $e->getMessage() . "</span><br>";
                        echo "&nbsp;&nbsp;💡 <em>This is normal if you're using JSON mode or haven't set up MySQL yet</em><br>";
                    }
                } else {
                    echo "✗ <span style='color:red'>Database constants not defined</span><br>";
                    echo "&nbsp;&nbsp;💡 <em>Missing DB_HOST, DB_NAME, DB_USER, or DB_PASS constants</em><br>";
                }
            } else {
                echo "📁 Testing JSON file storage...<br>";
                $data_dir = __DIR__ . '/../data/';

                if (is_dir($data_dir)) {
                    echo "✓ Data directory exists: <code>" . $data_dir . "</code><br>";

                    if (is_readable($data_dir)) {
                        echo "✓ Data directory is readable<br>";

                        // Check for key files
                        $key_files = ['users.json', 'config.php', 'admin_settings.json'];
                        foreach ($key_files as $file) {
                            $file_path = $data_dir . $file;
                            if (file_exists($file_path)) {
                                $file_size = filesize($file_path);
                                echo "✓ Found $file (" . number_format($file_size) . " bytes)<br>";
                            } else {
                                echo "⚠ <span style='color:orange'>$file not found (may be created automatically)</span><br>";
                            }
                        }

                        // Test reading users.json if it exists
                        $users_file = $data_dir . 'users.json';
                        if (file_exists($users_file)) {
                            try {
                                $users_content = file_get_contents($users_file);
                                $users_data = json_decode($users_content, true);
                                if (is_array($users_data)) {
                                    echo "✓ <span style='color:green'><strong>JSON data loaded successfully - Found " . count($users_data) . " users</strong></span><br>";
                                } else {
                                    echo "⚠ <span style='color:orange'>JSON file exists but data format issue</span><br>";
                                }
                            } catch (Exception $e) {
                                echo "✗ <span style='color:red'>Error reading JSON data: " . $e->getMessage() . "</span><br>";
                            }
                        }

                    } else {
                        echo "✗ <span style='color:red'>Data directory not readable</span><br>";
                    }
                } else {
                    echo "✗ <span style='color:red'>Data directory not found: <code>" . $data_dir . "</code></span><br>";
                    echo "&nbsp;&nbsp;💡 <em>This directory should contain your bot's data files</em><br>";
                }
            }
        } else {
            echo "✗ <span style='color:red'>STORAGE_MODE not defined in config</span><br>";
            echo "&nbsp;&nbsp;💡 <em>Check if config.php is properly configured</em><br>";
        }
    } else {
        echo "✗ <span style='color:red'>Config file not found</span><br>";
    }
} catch (Exception $e) {
    echo "✗ <span style='color:red'>Error during database test: " . $e->getMessage() . "</span><br>";
}
echo "<br>";

// Check for common issues
echo "<h3>7. Common Issues Check</h3>";

// Check if .htaccess exists and might be causing issues
if (file_exists(__DIR__ . '/.htaccess')) {
    echo "⚠ .htaccess file found - might be causing issues<br>";
} else {
    echo "✓ No .htaccess file found<br>";
}

// Check if index.php is accessible
if (file_exists(__DIR__ . '/index.php')) {
    echo "✓ index.php exists<br>";
} else {
    echo "✗ <span style='color:red'>index.php not found</span><br>";
}

echo "<br>";
echo "<h3>8. Error Log Location</h3>";
echo "PHP Error Log: " . (ini_get('error_log') ?: 'Not set') . "<br>";
echo "Check your hosting control panel for error logs<br>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check the issues marked with ✗ above</li>";
echo "<li>Look at your hosting control panel error logs</li>";
echo "<li>Fix file permissions if needed</li>";
echo "<li>Address any missing extensions or configuration issues</li>";
echo "</ol>";
?>
