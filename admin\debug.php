<?php
// Debug script to identify HTTP 500 errors
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>Admin Panel Debug Information</h2>";
echo "<hr>";

// Check PHP version
echo "<h3>1. PHP Environment</h3>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "<br>";

// Check file permissions
echo "<h3>2. File Permissions</h3>";
$files_to_check = [
    'config.php',
    'login.php', 
    'dashboard.php',
    'data_access.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $perms = substr(sprintf('%o', fileperms($filepath)), -4);
        echo "✓ $file: $perms<br>";
    } else {
        echo "✗ $file: <span style='color:red'>NOT FOUND</span><br>";
    }
}

// Check directory permissions
$dirs_to_check = ['cache', 'includes'];
foreach ($dirs_to_check as $dir) {
    $dirpath = __DIR__ . '/' . $dir;
    if (is_dir($dirpath)) {
        $perms = substr(sprintf('%o', fileperms($dirpath)), -4);
        $writable = is_writable($dirpath) ? 'WRITABLE' : 'NOT WRITABLE';
        echo "✓ $dir/: $perms ($writable)<br>";
    } else {
        echo "✗ $dir/: <span style='color:red'>NOT FOUND</span><br>";
    }
}
echo "<br>";

// Check required PHP extensions
echo "<h3>3. PHP Extensions</h3>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✓' : '✗';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<span style='color:$color'>$status $ext</span><br>";
}
echo "<br>";

// Check memory and limits
echo "<h3>4. PHP Configuration</h3>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Error Reporting: " . ini_get('error_reporting') . "<br>";
echo "Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "<br>";
echo "<br>";

// Test basic file inclusion
echo "<h3>5. File Inclusion Test</h3>";
try {
    if (file_exists(__DIR__ . '/config.php')) {
        echo "✓ config.php exists<br>";
        
        // Try to include without executing
        $config_content = file_get_contents(__DIR__ . '/config.php');
        if ($config_content !== false) {
            echo "✓ config.php readable<br>";
            
            // Check for syntax errors
            $syntax_check = php_check_syntax(__DIR__ . '/config.php');
            if ($syntax_check) {
                echo "✓ config.php syntax OK<br>";
            } else {
                echo "✗ <span style='color:red'>config.php has syntax errors</span><br>";
            }
        } else {
            echo "✗ <span style='color:red'>config.php not readable</span><br>";
        }
    } else {
        echo "✗ <span style='color:red'>config.php not found</span><br>";
    }
} catch (Exception $e) {
    echo "✗ <span style='color:red'>Error checking config.php: " . $e->getMessage() . "</span><br>";
}
echo "<br>";

// Test database connection (if config exists)
echo "<h3>6. Database Connection Test</h3>";
try {
    if (file_exists(__DIR__ . '/config.php')) {
        // Safely include config
        ob_start();
        include_once __DIR__ . '/config.php';
        $config_output = ob_get_clean();
        
        if (defined('STORAGE_MODE')) {
            echo "✓ STORAGE_MODE: " . STORAGE_MODE . "<br>";
            
            if (STORAGE_MODE === 'mysql') {
                if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                    echo "✓ Database constants defined<br>";
                    
                    try {
                        $pdo = new PDO(
                            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                            DB_USER,
                            DB_PASS,
                            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                        );
                        echo "✓ <span style='color:green'>Database connection successful</span><br>";
                    } catch (PDOException $e) {
                        echo "✗ <span style='color:red'>Database connection failed: " . $e->getMessage() . "</span><br>";
                    }
                } else {
                    echo "✗ <span style='color:red'>Database constants not defined</span><br>";
                }
            } else {
                echo "✓ JSON mode - checking data directory<br>";
                $data_dir = __DIR__ . '/../data/';
                if (is_dir($data_dir)) {
                    echo "✓ Data directory exists<br>";
                    if (is_readable($data_dir)) {
                        echo "✓ Data directory readable<br>";
                    } else {
                        echo "✗ <span style='color:red'>Data directory not readable</span><br>";
                    }
                } else {
                    echo "✗ <span style='color:red'>Data directory not found</span><br>";
                }
            }
        } else {
            echo "✗ <span style='color:red'>STORAGE_MODE not defined</span><br>";
        }
    }
} catch (Exception $e) {
    echo "✗ <span style='color:red'>Error testing database: " . $e->getMessage() . "</span><br>";
}
echo "<br>";

// Check for common issues
echo "<h3>7. Common Issues Check</h3>";

// Check if .htaccess exists and might be causing issues
if (file_exists(__DIR__ . '/.htaccess')) {
    echo "⚠ .htaccess file found - might be causing issues<br>";
} else {
    echo "✓ No .htaccess file found<br>";
}

// Check if index.php is accessible
if (file_exists(__DIR__ . '/index.php')) {
    echo "✓ index.php exists<br>";
} else {
    echo "✗ <span style='color:red'>index.php not found</span><br>";
}

echo "<br>";
echo "<h3>8. Error Log Location</h3>";
echo "PHP Error Log: " . (ini_get('error_log') ?: 'Not set') . "<br>";
echo "Check your hosting control panel for error logs<br>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check the issues marked with ✗ above</li>";
echo "<li>Look at your hosting control panel error logs</li>";
echo "<li>Fix file permissions if needed</li>";
echo "<li>Address any missing extensions or configuration issues</li>";
echo "</ol>";
?>
