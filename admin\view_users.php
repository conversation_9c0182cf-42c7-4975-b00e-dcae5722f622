<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load users
$users = [];
$error = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (!is_array($users)) {
                $users = [];
                $error = 'Invalid JSON format';
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$total_users = count($users);
$total_pages = ceil($total_users / $per_page);
$offset = ($page - 1) * $per_page;
$users_page = array_slice($users, $offset, $per_page, true);
?>
<!DOCTYPE html>
<html>
<head>
    <title>View Users</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: #007bff; color: white; padding: 15px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1200px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; }
        .users-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; }
        tr:hover { background: #f8f9fa; }
        .pagination { text-align: center; margin: 20px 0; }
        .pagination a { display: inline-block; padding: 8px 12px; margin: 0 2px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .pagination a:hover { background: #0056b3; }
        .pagination .current { background: #28a745; }
        .stats-bar { background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 View Users</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Admin</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php else: ?>
            <div class="success">✅ Loaded <?php echo number_format($total_users); ?> users successfully</div>
        <?php endif; ?>
        
        <div class="stats-bar">
            <strong>Total Users:</strong> <?php echo number_format($total_users); ?> | 
            <strong>Page:</strong> <?php echo $page; ?> of <?php echo $total_pages; ?> | 
            <strong>Showing:</strong> <?php echo count($users_page); ?> users
        </div>
        
        <?php if (!empty($users_page)): ?>
            <div class="users-table">
                <table>
                    <thead>
                        <tr>
                            <th>User ID</th>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Balance</th>
                            <th>Withdrawals</th>
                            <th>Referrals</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users_page as $user_id => $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user_id); ?></td>
                                <td><?php echo htmlspecialchars($user['first_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($user['username'] ?? 'N/A'); ?></td>
                                <td>₹<?php echo number_format($user['balance'] ?? 0, 2); ?></td>
                                <td>₹<?php echo number_format($user['successful_withdraw'] ?? 0, 2); ?></td>
                                <td><?php echo count($user['promotion_report'] ?? []); ?></td>
                                <td>
                                    <?php if ($user['banned'] ?? false): ?>
                                        <span style="color: red;">❌ Banned</span>
                                    <?php else: ?>
                                        <span style="color: green;">✅ Active</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>">← Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>" <?php echo $i === $page ? 'class="current"' : ''; ?>>
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>">Next →</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
