-- Database Optimization for InstantoPay Bot Admin Panel
-- This file contains recommended database indexes and optimizations for handling 100k+ users

-- =====================================================
-- MYSQL DATABASE OPTIMIZATIONS
-- =====================================================

-- If using MySQL storage mode, apply these optimizations:

-- 1. Create indexes for faster user queries
-- =====================================================

-- Index for user ID lookups (primary key should already exist)
-- ALTER TABLE users ADD PRIMARY KEY (user_id);

-- Index for username searches
-- CREATE INDEX idx_username ON users (username);

-- Index for first name searches
-- CREATE INDEX idx_first_name ON users (first_name);

-- Index for banned status filtering
-- CREATE INDEX idx_banned ON users (banned);

-- Index for referred status filtering
-- CREATE INDEX idx_referred ON users (referred);

-- Index for balance range queries
-- CREATE INDEX idx_balance ON users (balance);

-- Index for withdrawal status
-- CREATE INDEX idx_withdraw_review ON users (withdraw_under_review);

-- Index for successful withdrawals
-- CREATE INDEX idx_successful_withdraw ON users (successful_withdraw);

-- Composite index for common filter combinations
-- CREATE INDEX idx_status_balance ON users (banned, balance);
-- CREATE INDEX idx_referred_balance ON users (referred, balance);

-- 2. Optimize table structure for large datasets
-- =====================================================

-- Set appropriate storage engine (InnoDB recommended)
-- ALTER TABLE users ENGINE=InnoDB;

-- Set appropriate character set
-- ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. Query optimization settings
-- =====================================================

-- Recommended MySQL configuration for large datasets:
/*
[mysqld]
# Buffer pool size (set to 70-80% of available RAM)
innodb_buffer_pool_size = 1G

# Log file size
innodb_log_file_size = 256M

# Query cache (if using MySQL < 8.0)
query_cache_type = 1
query_cache_size = 128M

# Connection settings
max_connections = 200
wait_timeout = 300

# Performance settings
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Temporary table settings
tmp_table_size = 128M
max_heap_table_size = 128M

# Sort and join buffer sizes
sort_buffer_size = 2M
join_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 2M
*/

-- =====================================================
-- JSON FILE OPTIMIZATIONS
-- =====================================================

-- For JSON storage mode, consider these optimizations:

-- 1. File structure optimization
-- Split large users.json into smaller chunks:
-- users_1.json (users 1-10000)
-- users_2.json (users 10001-20000)
-- etc.

-- 2. Indexing for JSON files
-- Create separate index files for fast lookups:

-- user_index.json structure:
/*
{
    "by_id": {
        "123456": {"file": "users_1.json", "offset": 1234},
        "123457": {"file": "users_1.json", "offset": 2345}
    },
    "by_username": {
        "john_doe": "123456",
        "jane_smith": "123457"
    },
    "by_status": {
        "banned": ["123456"],
        "active": ["123457"]
    }
}
*/

-- 3. Caching strategy for JSON
-- Implement multi-level caching:
-- Level 1: In-memory cache for frequently accessed users
-- Level 2: File-based cache for computed statistics
-- Level 3: Compressed cache for large datasets

-- =====================================================
-- PERFORMANCE MONITORING QUERIES
-- =====================================================

-- Monitor query performance (MySQL):

-- Check slow queries
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SHOW VARIABLES LIKE 'long_query_time';

-- Analyze table usage
-- SHOW TABLE STATUS LIKE 'users';

-- Check index usage
-- SHOW INDEX FROM users;

-- Analyze query execution plans
-- EXPLAIN SELECT * FROM users WHERE banned = 0 AND balance > 1000;

-- Monitor buffer pool usage
-- SHOW STATUS LIKE 'Innodb_buffer_pool%';

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Regular maintenance tasks:

-- 1. Analyze tables monthly
-- ANALYZE TABLE users;

-- 2. Optimize tables quarterly
-- OPTIMIZE TABLE users;

-- 3. Check table integrity
-- CHECK TABLE users;

-- 4. Update table statistics
-- ANALYZE TABLE users UPDATE HISTOGRAM ON user_id, balance, banned;

-- =====================================================
-- BACKUP AND RECOVERY
-- =====================================================

-- Backup strategy for large datasets:

-- 1. Daily incremental backups
-- mysqldump --single-transaction --routines --triggers --databases bot_db > backup_$(date +%Y%m%d).sql

-- 2. Weekly full backups
-- mysqldump --single-transaction --routines --triggers --all-databases > full_backup_$(date +%Y%m%d).sql

-- 3. Point-in-time recovery setup
-- Enable binary logging:
-- log-bin = mysql-bin
-- expire_logs_days = 7

-- =====================================================
-- SCALING RECOMMENDATIONS
-- =====================================================

-- For datasets larger than 1 million users:

-- 1. Consider table partitioning
/*
ALTER TABLE users PARTITION BY RANGE (user_id) (
    PARTITION p0 VALUES LESS THAN (100000),
    PARTITION p1 VALUES LESS THAN (200000),
    PARTITION p2 VALUES LESS THAN (300000),
    PARTITION p3 VALUES LESS THAN MAXVALUE
);
*/

-- 2. Implement read replicas
-- Set up master-slave replication for read scaling

-- 3. Consider sharding
-- Split users across multiple databases based on user_id ranges

-- 4. Use connection pooling
-- Implement connection pooling to reduce connection overhead

-- =====================================================
-- SECURITY CONSIDERATIONS
-- =====================================================

-- Database security best practices:

-- 1. Create dedicated database user for admin panel
-- CREATE USER 'admin_panel'@'localhost' IDENTIFIED BY 'strong_password';
-- GRANT SELECT, UPDATE ON bot_db.users TO 'admin_panel'@'localhost';

-- 2. Limit privileges
-- REVOKE ALL PRIVILEGES ON *.* FROM 'admin_panel'@'localhost';
-- GRANT SELECT, UPDATE (banned) ON bot_db.users TO 'admin_panel'@'localhost';

-- 3. Enable SSL connections
-- REQUIRE SSL for database connections

-- 4. Regular security updates
-- Keep MySQL/MariaDB updated to latest stable version

-- =====================================================
-- MONITORING AND ALERTING
-- =====================================================

-- Set up monitoring for:

-- 1. Database performance metrics
-- - Query response times
-- - Connection counts
-- - Buffer pool hit ratio
-- - Disk I/O usage

-- 2. Storage metrics
-- - Database size growth
-- - Index size
-- - Free space

-- 3. Security metrics
-- - Failed login attempts
-- - Unusual query patterns
-- - Connection anomalies

-- =====================================================
-- TROUBLESHOOTING COMMON ISSUES
-- =====================================================

-- Common performance issues and solutions:

-- 1. Slow user searches
-- Solution: Ensure proper indexes on search fields

-- 2. High memory usage
-- Solution: Optimize buffer pool size and query cache

-- 3. Lock contention
-- Solution: Use appropriate isolation levels and shorter transactions

-- 4. Disk space issues
-- Solution: Implement log rotation and regular cleanup

-- 5. Connection timeouts
-- Solution: Optimize connection pooling and timeout settings

-- =====================================================
-- TESTING AND VALIDATION
-- =====================================================

-- Performance testing queries:

-- Test user search performance
-- SELECT SQL_NO_CACHE * FROM users WHERE first_name LIKE 'John%' LIMIT 50;

-- Test pagination performance
-- SELECT SQL_NO_CACHE * FROM users ORDER BY user_id DESC LIMIT 50 OFFSET 10000;

-- Test filter performance
-- SELECT SQL_NO_CACHE COUNT(*) FROM users WHERE banned = 0 AND balance > 1000;

-- Test aggregation performance
-- SELECT SQL_NO_CACHE SUM(balance), COUNT(*), AVG(balance) FROM users WHERE banned = 0;
