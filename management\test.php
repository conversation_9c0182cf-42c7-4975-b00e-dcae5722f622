<?php
/**
 * Test file to verify admin panel directory is accessible
 * This file helps diagnose 403 Forbidden errors
 */

// Display basic information
echo "<h1>✅ Admin Panel Directory Test</h1>";
echo "<p><strong>Status:</strong> Directory is accessible!</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";

// Test file permissions
echo "<h2>📁 Directory Permissions Test</h2>";
$cacheDir = __DIR__ . '/cache';
$logsDir = __DIR__ . '/logs';

echo "<p><strong>Cache Directory:</strong> ";
if (is_dir($cacheDir)) {
    echo "✅ Exists | ";
    echo (is_writable($cacheDir) ? "✅ Writable" : "❌ Not Writable");
} else {
    echo "❌ Does not exist";
}
echo "</p>";

echo "<p><strong>Logs Directory:</strong> ";
if (is_dir($logsDir)) {
    echo "✅ Exists | ";
    echo (is_writable($logsDir) ? "✅ Writable" : "❌ Not Writable");
} else {
    echo "❌ Does not exist";
}
echo "</p>";

// Test core files
echo "<h2>📄 Core Files Test</h2>";
$coreFiles = ['config.php', 'auth.php', 'DataProcessor.php', 'login.php', 'dashboard.php'];
foreach ($coreFiles as $file) {
    $filePath = __DIR__ . '/' . $file;
    echo "<p><strong>$file:</strong> ";
    if (file_exists($filePath)) {
        echo "✅ Exists | ";
        echo (is_readable($filePath) ? "✅ Readable" : "❌ Not Readable");
    } else {
        echo "❌ Missing";
    }
    echo "</p>";
}

// Test database connection (if applicable)
echo "<h2>🔗 Bot Integration Test</h2>";
$botConfigPath = dirname(__DIR__) . '/config.php';
echo "<p><strong>Bot Config:</strong> ";
if (file_exists($botConfigPath)) {
    echo "✅ Found at " . $botConfigPath;
} else {
    echo "❌ Not found";
}
echo "</p>";

// Next steps
echo "<h2>🚀 Next Steps</h2>";
echo "<p>If you see this page, the directory access issue is resolved!</p>";
echo "<p><strong>Try these links:</strong></p>";
echo "<ul>";
echo "<li><a href='login.php'>Admin Login Page</a></li>";
echo "<li><a href='index.php'>Admin Panel Home</a></li>";
echo "</ul>";

echo "<h2>🔧 Troubleshooting Info</h2>";
echo "<p><strong>Current URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
echo "<p><strong>Request Method:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</p>";
echo "<p><strong>User Agent:</strong> " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "</p>";

// Security check
echo "<h2>🛡️ Security Status</h2>";
echo "<p><strong>HTTPS:</strong> " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "✅ Enabled" : "⚠️ Not Enabled") . "</p>";
echo "<p><strong>Session Support:</strong> " . (function_exists('session_start') ? "✅ Available" : "❌ Not Available") . "</p>";
echo "<p><strong>JSON Support:</strong> " . (function_exists('json_encode') ? "✅ Available" : "❌ Not Available") . "</p>";

echo "<hr>";
echo "<p><small>Generated at: " . date('Y-m-d H:i:s T') . "</small></p>";
?>
