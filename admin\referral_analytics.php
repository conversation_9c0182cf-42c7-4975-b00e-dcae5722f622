<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load users
$users = [];
$error = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (!is_array($users)) {
                $users = [];
                $error = 'Invalid JSON format';
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Calculate referral analytics
$analytics = [
    'total_referrals' => 0,
    'total_referral_earnings' => 0,
    'unique_referrers' => 0,
    'referred_users' => 0,
    'avg_referrals_per_user' => 0,
    'avg_earnings_per_referral' => 0,
    'conversion_rate' => 0,
    'top_referrer_earnings' => 0,
    'referral_levels' => []
];

$referrers = [];
$referred_users_count = 0;
$all_referrals = [];

// Process users for referral data
foreach ($users as $user_id => $user) {
    // Count referred users
    if ($user['referred'] ?? false) {
        $referred_users_count++;
    }
    
    // Process referral reports
    if (isset($user['promotion_report']) && is_array($user['promotion_report'])) {
        $user_referrals = count($user['promotion_report']);
        $user_earnings = array_sum(array_column($user['promotion_report'], 'amount_got'));
        
        if ($user_referrals > 0) {
            $referrers[$user_id] = [
                'user_id' => $user_id,
                'name' => $user['first_name'] ?? 'Unknown',
                'username' => $user['username'] ?? '',
                'referral_count' => $user_referrals,
                'total_earnings' => $user_earnings,
                'avg_earnings' => $user_earnings / $user_referrals,
                'success_rate' => 100 // Assuming all referrals in promotion_report are successful
            ];
            
            $analytics['total_referrals'] += $user_referrals;
            $analytics['total_referral_earnings'] += $user_earnings;
            
            // Store individual referrals for analysis
            foreach ($user['promotion_report'] as $referral) {
                $all_referrals[] = [
                    'referrer_id' => $user_id,
                    'referrer_name' => $user['first_name'] ?? 'Unknown',
                    'referred_user_id' => $referral['referred_user_id'] ?? '',
                    'referred_user_name' => $referral['referred_user_name'] ?? 'Unknown',
                    'amount' => $referral['amount_got'] ?? 0,
                    'date' => $referral['created_at'] ?? date('Y-m-d H:i:s')
                ];
            }
        }
    }
}

// Calculate analytics
$analytics['unique_referrers'] = count($referrers);
$analytics['referred_users'] = $referred_users_count;
$total_users = count($users);
$analytics['conversion_rate'] = $total_users > 0 ? ($referred_users_count / $total_users) * 100 : 0;
$analytics['avg_referrals_per_user'] = $analytics['unique_referrers'] > 0 ? 
    $analytics['total_referrals'] / $analytics['unique_referrers'] : 0;
$analytics['avg_earnings_per_referral'] = $analytics['total_referrals'] > 0 ? 
    $analytics['total_referral_earnings'] / $analytics['total_referrals'] : 0;

// Sort referrers by different metrics
$top_by_count = $referrers;
$top_by_earnings = $referrers;
$top_by_avg_earnings = $referrers;

usort($top_by_count, function($a, $b) { return $b['referral_count'] - $a['referral_count']; });
usort($top_by_earnings, function($a, $b) { return $b['total_earnings'] - $a['total_earnings']; });
usort($top_by_avg_earnings, function($a, $b) { return $b['avg_earnings'] - $a['avg_earnings']; });

$top_by_count = array_slice($top_by_count, 0, 10);
$top_by_earnings = array_slice($top_by_earnings, 0, 10);
$top_by_avg_earnings = array_slice($top_by_avg_earnings, 0, 10);

if (!empty($top_by_earnings)) {
    $analytics['top_referrer_earnings'] = $top_by_earnings[0]['total_earnings'];
}

// Referral level analysis
$level_stats = [
    '1-5' => ['count' => 0, 'earnings' => 0],
    '6-10' => ['count' => 0, 'earnings' => 0],
    '11-25' => ['count' => 0, 'earnings' => 0],
    '26-50' => ['count' => 0, 'earnings' => 0],
    '50+' => ['count' => 0, 'earnings' => 0]
];

foreach ($referrers as $referrer) {
    $count = $referrer['referral_count'];
    $earnings = $referrer['total_earnings'];
    
    if ($count <= 5) {
        $level_stats['1-5']['count']++;
        $level_stats['1-5']['earnings'] += $earnings;
    } elseif ($count <= 10) {
        $level_stats['6-10']['count']++;
        $level_stats['6-10']['earnings'] += $earnings;
    } elseif ($count <= 25) {
        $level_stats['11-25']['count']++;
        $level_stats['11-25']['earnings'] += $earnings;
    } elseif ($count <= 50) {
        $level_stats['26-50']['count']++;
        $level_stats['26-50']['earnings'] += $earnings;
    } else {
        $level_stats['50+']['count']++;
        $level_stats['50+']['earnings'] += $earnings;
    }
}

// Recent referrals (last 50)
$recent_referrals = array_slice(array_reverse($all_referrals), 0, 50);

// Monthly referral trends (last 6 months)
$monthly_trends = [];
for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $monthly_trends[$month] = [
        'month' => date('M Y', strtotime("-$i months")),
        'referrals' => 0,
        'earnings' => 0,
        'new_referrers' => 0
    ];
}

foreach ($all_referrals as $referral) {
    $month = date('Y-m', strtotime($referral['date']));
    if (isset($monthly_trends[$month])) {
        $monthly_trends[$month]['referrals']++;
        $monthly_trends[$month]['earnings'] += $referral['amount'];
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Referral Analytics</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #6f42c1, #e83e8c); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 10px; }
        .metric-label { color: #666; font-size: 0.9em; text-transform: uppercase; letter-spacing: 1px; }
        .metric-change { font-size: 0.8em; margin-top: 5px; color: #28a745; }
        .referral { color: #6f42c1; }
        .earnings { color: #28a745; }
        .users { color: #007bff; }
        .rate { color: #17a2b8; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .leaderboards-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .leaderboard { max-height: 400px; overflow-y: auto; }
        .leaderboard-item { display: flex; justify-content: space-between; align-items: center; padding: 12px; border-bottom: 1px solid #eee; }
        .leaderboard-item:hover { background: #f8f9fa; }
        .rank { width: 30px; font-weight: bold; color: #6f42c1; }
        .user-info { flex: 1; margin-left: 10px; }
        .user-name { font-weight: bold; }
        .user-details { font-size: 0.8em; color: #666; }
        .user-value { font-weight: bold; text-align: right; }
        .level-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .level-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #6f42c1; }
        .level-card h4 { margin: 0 0 10px 0; color: #6f42c1; }
        .recent-referrals { max-height: 400px; overflow-y: auto; }
        .referral-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; font-size: 0.9em; }
        .referral-item:hover { background: #f8f9fa; }
        .referral-info { flex: 1; }
        .referral-amount { font-weight: bold; color: #28a745; }
        .referral-date { color: #666; font-size: 0.8em; }
        .trends-container { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .trends-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; }
        .trend-item { background: white; padding: 15px; border-radius: 6px; text-align: center; }
        .trend-month { font-size: 0.8em; color: #666; margin-bottom: 5px; }
        .trend-value { font-weight: bold; color: #6f42c1; }
        .trend-earnings { font-size: 0.8em; color: #28a745; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.error { background: #f8d7da; color: #721c24; }
        .message.info { background: #d1ecf1; color: #0c5460; }
        .summary-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-item { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .summary-item strong { display: block; font-size: 1.2em; color: #333; margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 Referral Analytics</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Key Referral Metrics -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value referral"><?php echo number_format($analytics['total_referrals']); ?></div>
                <div class="metric-label">Total Referrals</div>
                <div class="metric-change">All successful referrals</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value earnings">₹<?php echo number_format($analytics['total_referral_earnings'], 2); ?></div>
                <div class="metric-label">Total Earnings</div>
                <div class="metric-change">Commission paid out</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value users"><?php echo number_format($analytics['unique_referrers']); ?></div>
                <div class="metric-label">Active Referrers</div>
                <div class="metric-change">Users with referrals</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value rate"><?php echo number_format($analytics['conversion_rate'], 1); ?>%</div>
                <div class="metric-label">Referral Rate</div>
                <div class="metric-change">Users who were referred</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value referral"><?php echo number_format($analytics['avg_referrals_per_user'], 1); ?></div>
                <div class="metric-label">Avg Referrals/User</div>
                <div class="metric-change">Per active referrer</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value earnings">₹<?php echo number_format($analytics['avg_earnings_per_referral'], 2); ?></div>
                <div class="metric-label">Avg Earnings/Referral</div>
                <div class="metric-change">Commission per referral</div>
            </div>
        </div>
        
        <!-- Monthly Trends -->
        <div class="content-section">
            <h3>📊 Monthly Referral Trends</h3>
            <div class="trends-container">
                <div class="trends-grid">
                    <?php foreach ($monthly_trends as $trend): ?>
                        <div class="trend-item">
                            <div class="trend-month"><?php echo $trend['month']; ?></div>
                            <div class="trend-value"><?php echo number_format($trend['referrals']); ?></div>
                            <div class="trend-earnings">₹<?php echo number_format($trend['earnings'], 0); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Referrer Level Analysis -->
        <div class="content-section">
            <h3>📊 Referrer Performance Levels</h3>
            <div class="level-stats">
                <?php foreach ($level_stats as $level => $stats): ?>
                    <div class="level-card">
                        <h4><?php echo $level; ?> Referrals</h4>
                        <div><strong><?php echo $stats['count']; ?></strong> users</div>
                        <div style="color: #28a745; font-size: 0.9em;">₹<?php echo number_format($stats['earnings'], 0); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Leaderboards -->
        <div class="content-section">
            <h3>🏆 Top Referrers</h3>
            <div class="leaderboards-grid">
                <!-- Top by Count -->
                <div>
                    <h4>🔢 Most Referrals</h4>
                    <div class="leaderboard">
                        <?php foreach ($top_by_count as $index => $referrer): ?>
                            <div class="leaderboard-item">
                                <div class="rank">#<?php echo $index + 1; ?></div>
                                <div class="user-info">
                                    <div class="user-name"><?php echo htmlspecialchars($referrer['name']); ?></div>
                                    <div class="user-details">
                                        <?php if ($referrer['username']): ?>
                                            @<?php echo htmlspecialchars($referrer['username']); ?> • 
                                        <?php endif; ?>
                                        ID: <?php echo htmlspecialchars($referrer['user_id']); ?>
                                    </div>
                                </div>
                                <div class="user-value">
                                    <div style="color: #6f42c1;"><?php echo number_format($referrer['referral_count']); ?></div>
                                    <div style="font-size: 0.8em; color: #28a745;">₹<?php echo number_format($referrer['total_earnings'], 0); ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Top by Earnings -->
                <div>
                    <h4>💰 Highest Earnings</h4>
                    <div class="leaderboard">
                        <?php foreach ($top_by_earnings as $index => $referrer): ?>
                            <div class="leaderboard-item">
                                <div class="rank">#<?php echo $index + 1; ?></div>
                                <div class="user-info">
                                    <div class="user-name"><?php echo htmlspecialchars($referrer['name']); ?></div>
                                    <div class="user-details">
                                        <?php if ($referrer['username']): ?>
                                            @<?php echo htmlspecialchars($referrer['username']); ?> • 
                                        <?php endif; ?>
                                        ID: <?php echo htmlspecialchars($referrer['user_id']); ?>
                                    </div>
                                </div>
                                <div class="user-value">
                                    <div style="color: #28a745;">₹<?php echo number_format($referrer['total_earnings'], 2); ?></div>
                                    <div style="font-size: 0.8em; color: #6f42c1;"><?php echo number_format($referrer['referral_count']); ?> refs</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Top by Average -->
                <div>
                    <h4>⭐ Best Average Earnings</h4>
                    <div class="leaderboard">
                        <?php foreach ($top_by_avg_earnings as $index => $referrer): ?>
                            <div class="leaderboard-item">
                                <div class="rank">#<?php echo $index + 1; ?></div>
                                <div class="user-info">
                                    <div class="user-name"><?php echo htmlspecialchars($referrer['name']); ?></div>
                                    <div class="user-details">
                                        <?php if ($referrer['username']): ?>
                                            @<?php echo htmlspecialchars($referrer['username']); ?> • 
                                        <?php endif; ?>
                                        ID: <?php echo htmlspecialchars($referrer['user_id']); ?>
                                    </div>
                                </div>
                                <div class="user-value">
                                    <div style="color: #17a2b8;">₹<?php echo number_format($referrer['avg_earnings'], 2); ?></div>
                                    <div style="font-size: 0.8em; color: #6c757d;">per referral</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Referrals -->
        <div class="content-section">
            <h3>🕒 Recent Referrals</h3>
            <?php if (!empty($recent_referrals)): ?>
                <div class="recent-referrals">
                    <?php foreach ($recent_referrals as $referral): ?>
                        <div class="referral-item">
                            <div class="referral-info">
                                <strong><?php echo htmlspecialchars($referral['referrer_name']); ?></strong> 
                                referred <strong><?php echo htmlspecialchars($referral['referred_user_name']); ?></strong>
                                <div class="referral-date"><?php echo date('M d, Y H:i', strtotime($referral['date'])); ?></div>
                            </div>
                            <div class="referral-amount">₹<?php echo number_format($referral['amount'], 2); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="message info">No recent referrals found.</div>
            <?php endif; ?>
        </div>
        
        <!-- Performance Summary -->
        <div class="content-section">
            <h3>📋 Performance Summary</h3>
            <div class="summary-stats">
                <div class="summary-item">
                    <strong><?php echo number_format(($analytics['unique_referrers'] / max(1, $total_users)) * 100, 1); ?>%</strong>
                    <div>Users are Referrers</div>
                </div>
                <div class="summary-item">
                    <strong><?php echo number_format($analytics['conversion_rate'], 1); ?>%</strong>
                    <div>Users were Referred</div>
                </div>
                <div class="summary-item">
                    <strong>₹<?php echo number_format($analytics['total_referral_earnings'] / max(1, $total_users), 2); ?></strong>
                    <div>Avg Earnings per User</div>
                </div>
                <div class="summary-item">
                    <strong><?php echo !empty($top_by_earnings) ? number_format($top_by_earnings[0]['referral_count']) : '0'; ?></strong>
                    <div>Top Referrer Count</div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="content-section">
            <h3>⚡ Quick Actions</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="referral_chains.php" style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    🔗 View Referral Chains
                </a>
                <a href="user_management.php?filter=referred" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    👥 View Referred Users
                </a>
                <a href="financial_analytics.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    💰 Financial Analytics
                </a>
            </div>
        </div>
    </div>
</body>
</html>
