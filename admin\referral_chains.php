<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Increase memory limit for large JSON files
ini_set('memory_limit', '512M');

// Load users
$users = [];
$users_by_id = [];
$error = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users_array = json_decode($content, true);
            if (!is_array($users_array)) {
                $users = [];
                $error = 'Invalid JSON format';
            } else {
                // Convert array to associative array with user_id as key
                foreach ($users_array as $user) {
                    if (isset($user['user_id'])) {
                        $users_by_id[$user['user_id']] = $user;
                    }
                }
                $users = $users_by_id; // Use the converted format
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Get specific user or show top referrers
$selected_user_id = $_GET['user_id'] ?? '';
$view_mode = $_GET['view'] ?? 'tree';

// Debug information (can be removed in production)
$debug_info = '';
if ($selected_user_id && !$selected_user) {
    $debug_info = "User ID $selected_user_id not found in database";
}

// Function to build referral tree
function buildReferralTree($users, $root_user_id) {
    $tree = [];

    foreach ($users as $user_id => $user) {
        // Handle both string and integer user IDs
        $referred_by = $user['referred_by'] ?? '';
        if ($referred_by == $root_user_id || $referred_by === (string)$root_user_id) {
            $actual_user_id = $user['user_id'] ?? $user_id;
            $tree[] = [
                'user_id' => $actual_user_id,
                'name' => $user['first_name'] ?? 'Unknown',
                'username' => $user['username'] ?? '',
                'balance' => $user['balance'] ?? 0,
                'referrals' => count($user['promotion_report'] ?? []),
                'children' => buildReferralTree($users, $actual_user_id)
            ];
        }
    }

    return $tree;
}

// Function to get referral chain (upward)
function getReferralChain($users, $user_id) {
    $chain = [];
    $current_id = $user_id;
    $depth = 0;

    while ($current_id && $depth < 10) {
        // Handle both string and integer user IDs
        $current_id_str = (string)$current_id;
        $current_id_int = (int)$current_id;

        $user = null;
        if (isset($users[$current_id])) {
            $user = $users[$current_id];
        } elseif (isset($users[$current_id_str])) {
            $user = $users[$current_id_str];
        } elseif (isset($users[$current_id_int])) {
            $user = $users[$current_id_int];
        }

        if (!$user) break;

        $chain[] = [
            'user_id' => $current_id,
            'name' => $user['first_name'] ?? 'Unknown',
            'username' => $user['username'] ?? '',
            'balance' => $user['balance'] ?? 0,
            'referrals' => count($user['promotion_report'] ?? []),
            'level' => $depth
        ];

        $referred_by = $user['referred_by'] ?? '';
        if ($referred_by === 'None' || $referred_by === '' || $referred_by === null) {
            break;
        }

        $current_id = $referred_by;
        $depth++;
    }

    return array_reverse($chain);
}

// Get top referrers
$top_referrers = [];
foreach ($users as $user_id => $user) {
    $referral_count = count($user['promotion_report'] ?? []);
    if ($referral_count > 0) {
        $top_referrers[] = [
            'user_id' => $user_id,
            'name' => $user['first_name'] ?? 'Unknown',
            'username' => $user['username'] ?? '',
            'referral_count' => $referral_count,
            'total_earnings' => array_sum(array_column($user['promotion_report'] ?? [], 'amount_got'))
        ];
    }
}

// Sort by referral count
usort($top_referrers, function($a, $b) {
    return $b['referral_count'] - $a['referral_count'];
});

$top_referrers = array_slice($top_referrers, 0, 20);

// Get data for selected user
$selected_user = null;
$referral_tree = [];
$referral_chain = [];

if ($selected_user_id) {
    // Handle both string and integer user IDs
    $selected_user_id_str = (string)$selected_user_id;
    $selected_user_id_int = (int)$selected_user_id;

    if (isset($users[$selected_user_id])) {
        $selected_user = $users[$selected_user_id];
        $actual_user_id = $selected_user_id;
    } elseif (isset($users[$selected_user_id_str])) {
        $selected_user = $users[$selected_user_id_str];
        $actual_user_id = $selected_user_id_str;
    } elseif (isset($users[$selected_user_id_int])) {
        $selected_user = $users[$selected_user_id_int];
        $actual_user_id = $selected_user_id_int;
    }

    if ($selected_user) {
        $selected_user['user_id'] = $actual_user_id;
        $referral_tree = buildReferralTree($users, $actual_user_id);
        $referral_chain = getReferralChain($users, $actual_user_id);
        // User found successfully
    } else {
        $debug_info = "User ID $selected_user_id not found in database";
    }
}

// Function to render tree HTML
function renderTreeHTML($tree, $level = 0) {
    if (empty($tree)) return '';
    
    $html = '<ul class="tree-level level-' . $level . '">';
    foreach ($tree as $node) {
        $html .= '<li class="tree-node">';
        $html .= '<div class="user-node">';
        $html .= '<div class="user-info">';
        $html .= '<strong>' . htmlspecialchars($node['name']) . '</strong>';
        if ($node['username']) {
            $html .= '<br><small>@' . htmlspecialchars($node['username']) . '</small>';
        }
        $html .= '<br><small>ID: ' . htmlspecialchars($node['user_id']) . '</small>';
        $html .= '</div>';
        $html .= '<div class="user-stats">';
        $html .= '<div>₹' . number_format($node['balance'], 2) . '</div>';
        $html .= '<div>' . $node['referrals'] . ' refs</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        if (!empty($node['children'])) {
            $html .= renderTreeHTML($node['children'], $level + 1);
        }
        
        $html .= '</li>';
    }
    $html .= '</ul>';
    
    return $html;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Referral Chain Visualization</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #6f42c1, #e83e8c); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .controls { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .controls-row { display: grid; grid-template-columns: 2fr 1fr auto; gap: 15px; align-items: end; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { padding: 10px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; }
        .btn { padding: 10px 20px; background: #6f42c1; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; text-align: center; }
        .btn:hover { background: #5a2d91; }
        .view-tabs { display: flex; gap: 10px; margin-bottom: 20px; }
        .view-tab { padding: 10px 20px; background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 5px; text-decoration: none; color: #495057; }
        .view-tab.active { background: #6f42c1; color: white; border-color: #6f42c1; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .top-referrers { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .referrer-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1; }
        .referrer-card:hover { background: #e9ecef; cursor: pointer; }
        .referrer-name { font-weight: bold; font-size: 1.1em; margin-bottom: 5px; }
        .referrer-stats { display: flex; justify-content: space-between; color: #666; font-size: 0.9em; }
        .tree-container { overflow-x: auto; padding: 20px; }
        .tree-level { list-style: none; padding-left: 0; margin: 0; }
        .tree-level.level-0 { padding-left: 0; }
        .tree-level:not(.level-0) { padding-left: 40px; position: relative; }
        .tree-level:not(.level-0):before { content: ''; position: absolute; left: 20px; top: 0; bottom: 0; width: 2px; background: #dee2e6; }
        .tree-node { margin: 10px 0; position: relative; }
        .tree-node:before { content: ''; position: absolute; left: -20px; top: 25px; width: 20px; height: 2px; background: #dee2e6; }
        .tree-node:last-child:after { content: ''; position: absolute; left: -20px; top: 25px; bottom: -10px; width: 2px; background: #f5f5f5; }
        .user-node { background: white; border: 2px solid #dee2e6; border-radius: 8px; padding: 15px; display: flex; justify-content: space-between; align-items: center; min-width: 250px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .user-node:hover { border-color: #6f42c1; box-shadow: 0 4px 10px rgba(0,0,0,0.15); }
        .user-info { flex: 1; }
        .user-stats { text-align: right; color: #666; font-size: 0.9em; }
        .user-stats div { margin: 2px 0; }
        .chain-container { display: flex; align-items: center; gap: 20px; overflow-x: auto; padding: 20px; }
        .chain-user { background: white; border: 2px solid #dee2e6; border-radius: 8px; padding: 15px; min-width: 200px; text-align: center; position: relative; }
        .chain-user.root { border-color: #6f42c1; background: #f8f4ff; }
        .chain-arrow { font-size: 24px; color: #6f42c1; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.error { background: #f8d7da; color: #721c24; }
        .message.info { background: #d1ecf1; color: #0c5460; }
        .stats-bar { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .stats-bar strong { color: #6f42c1; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 Referral Chain Visualization</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($debug_info): ?>
            <div class="message info">🔍 Debug: <?php echo htmlspecialchars($debug_info); ?></div>
        <?php endif; ?>
        
        <!-- User Search -->
        <div class="controls">
            <form method="GET" action="">
                <div class="controls-row">
                    <div class="form-group">
                        <label>Search User by ID or Name</label>
                        <input type="text" name="user_id" placeholder="Enter user ID or search name" value="<?php echo htmlspecialchars($selected_user_id); ?>">
                    </div>
                    <div class="form-group">
                        <label>View Mode</label>
                        <select name="view">
                            <option value="tree" <?php echo $view_mode === 'tree' ? 'selected' : ''; ?>>Referral Tree</option>
                            <option value="chain" <?php echo $view_mode === 'chain' ? 'selected' : ''; ?>>Referral Chain</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn">🔍 View</button>
                    </div>
                </div>
            </form>
        </div>
        
        <?php if ($selected_user): ?>
            <!-- Selected User Info -->
            <div class="content-section">
                <h3>👤 Selected User: <?php echo htmlspecialchars($selected_user['first_name']); ?></h3>
                <div class="stats-bar">
                    <strong>User ID:</strong> <?php echo htmlspecialchars($selected_user_id); ?> | 
                    <strong>Username:</strong> <?php echo htmlspecialchars($selected_user['username'] ?? 'N/A'); ?> | 
                    <strong>Balance:</strong> ₹<?php echo number_format($selected_user['balance'] ?? 0, 2); ?> | 
                    <strong>Direct Referrals:</strong> <?php echo count($referral_tree); ?> | 
                    <strong>Total Referrals:</strong> <?php echo count($selected_user['promotion_report'] ?? []); ?>
                </div>
            </div>
            
            <!-- View Tabs -->
            <div class="view-tabs">
                <a href="?user_id=<?php echo urlencode($selected_user_id); ?>&view=tree" class="view-tab <?php echo $view_mode === 'tree' ? 'active' : ''; ?>">
                    🌳 Referral Tree (Downward)
                </a>
                <a href="?user_id=<?php echo urlencode($selected_user_id); ?>&view=chain" class="view-tab <?php echo $view_mode === 'chain' ? 'active' : ''; ?>">
                    ⛓️ Referral Chain (Upward)
                </a>
            </div>
            
            <?php if ($view_mode === 'tree'): ?>
                <!-- Referral Tree -->
                <div class="content-section">
                    <h3>🌳 Referral Tree - Users Referred by <?php echo htmlspecialchars($selected_user['first_name']); ?></h3>
                    
                    <?php if (!empty($referral_tree)): ?>
                        <div class="tree-container">
                            <?php echo renderTreeHTML($referral_tree); ?>
                        </div>
                    <?php else: ?>
                        <div class="message info">This user hasn't referred anyone yet.</div>
                    <?php endif; ?>
                </div>
                
            <?php else: ?>
                <!-- Referral Chain -->
                <div class="content-section">
                    <h3>⛓️ Referral Chain - Who Referred Whom</h3>
                    
                    <?php if (!empty($referral_chain)): ?>
                        <div class="chain-container">
                            <?php foreach ($referral_chain as $index => $user): ?>
                                <div class="chain-user <?php echo $user['user_id'] === $selected_user_id ? 'root' : ''; ?>">
                                    <div><strong><?php echo htmlspecialchars($user['name']); ?></strong></div>
                                    <?php if ($user['username']): ?>
                                        <div><small>@<?php echo htmlspecialchars($user['username']); ?></small></div>
                                    <?php endif; ?>
                                    <div><small>ID: <?php echo htmlspecialchars($user['user_id']); ?></small></div>
                                    <div style="margin-top: 10px; color: #666;">
                                        <div>₹<?php echo number_format($user['balance'], 2); ?></div>
                                        <div><?php echo $user['referrals']; ?> referrals</div>
                                    </div>
                                    <?php if ($user['user_id'] === $selected_user_id): ?>
                                        <div style="margin-top: 5px; color: #6f42c1; font-weight: bold; font-size: 0.8em;">SELECTED USER</div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($index < count($referral_chain) - 1): ?>
                                    <div class="chain-arrow">→</div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="message info">
                            <strong>Chain Explanation:</strong> This shows the referral path from the top-level referrer down to the selected user. 
                            Each arrow (→) means "referred by".
                        </div>
                    <?php else: ?>
                        <div class="message info">This user was not referred by anyone (direct user).</div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- Top Referrers -->
            <div class="content-section">
                <h3>🏆 Top Referrers</h3>
                <p>Click on any user to view their referral chain and tree.</p>
                
                <?php if (!empty($top_referrers)): ?>
                    <div class="top-referrers">
                        <?php foreach ($top_referrers as $index => $referrer): ?>
                            <div class="referrer-card" onclick="location.href='?user_id=<?php echo urlencode($referrer['user_id']); ?>&view=tree'">
                                <div class="referrer-name">
                                    #<?php echo $index + 1; ?> <?php echo htmlspecialchars($referrer['name']); ?>
                                    <?php if ($referrer['username']): ?>
                                        <small>(@<?php echo htmlspecialchars($referrer['username']); ?>)</small>
                                    <?php endif; ?>
                                </div>
                                <div class="referrer-stats">
                                    <span><strong><?php echo $referrer['referral_count']; ?></strong> referrals</span>
                                    <span><strong>₹<?php echo number_format($referrer['total_earnings'], 2); ?></strong> earned</span>
                                </div>
                                <div style="margin-top: 5px; color: #666; font-size: 0.8em;">
                                    ID: <?php echo htmlspecialchars($referrer['user_id']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="message info">No referrals found in the system yet.</div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
