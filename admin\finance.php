<?php
$pageTitle = 'Financial Management';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();
$stats = $dataAccess->getDashboardStats();
$financialStats = $dataAccess->getFinancialStats();

// Calculate financial metrics
$totalRevenue = $stats['total_withdrawals'] + $stats['pending_withdrawals'];
$revenueGrowth = 15.2; // This would be calculated from historical data
$avgWithdrawalAmount = $stats['total_withdrawals'] > 0 ? $stats['total_withdrawals'] / max(1, $stats['total_users']) : 0;
$withdrawalSuccessRate = 95.8; // This would be calculated from withdrawal data
$commissionEarned = $totalRevenue * 0.05; // Assuming 5% commission
?>

<!-- Financial Overview Cards -->
<div class="row g-4 mb-4">
    <!-- Total Revenue -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="value"><?php echo formatCurrency($totalRevenue); ?></div>
            <div class="label">Total Revenue</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +<?php echo number_format($revenueGrowth, 1); ?>% growth
            </div>
        </div>
    </div>
    
    <!-- Total Withdrawals -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="value"><?php echo formatCurrency($stats['total_withdrawals']); ?></div>
            <div class="label">Completed Withdrawals</div>
            <div class="change">
                <i class="fas fa-percentage me-1"></i>
                <?php echo number_format($withdrawalSuccessRate, 1); ?>% success rate
            </div>
        </div>
    </div>
    
    <!-- Pending Withdrawals -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="value"><?php echo formatCurrency($stats['pending_withdrawals']); ?></div>
            <div class="label">Pending Withdrawals</div>
            <div class="change">
                <i class="fas fa-list me-1"></i>
                <?php echo number_format($stats['pending_withdrawal_requests'] ?? 0); ?> requests
            </div>
        </div>
    </div>
    
    <!-- Commission Earned -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                <i class="fas fa-coins"></i>
            </div>
            <div class="value"><?php echo formatCurrency($commissionEarned); ?></div>
            <div class="label">Commission Earned</div>
            <div class="change positive">
                <i class="fas fa-percentage me-1"></i>
                5% of revenue
            </div>
        </div>
    </div>
</div>

<!-- Financial Analytics Charts -->
<div class="row g-4 mb-4">
    <!-- Revenue Breakdown -->
    <div class="col-lg-8">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-area me-2 text-primary"></i>
                Revenue Analytics
            </h5>
            <div class="chart-container" style="height: 400px;">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Financial Distribution -->
    <div class="col-lg-4">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-pie me-2 text-success"></i>
                Financial Distribution
            </h5>
            <div class="chart-container">
                <canvas id="distributionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Financial Metrics -->
<div class="row g-4 mb-4">
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-calculator me-2 text-info"></i>
                Financial Metrics
            </h5>
            <div class="row g-3">
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo formatCurrency($avgWithdrawalAmount); ?></div>
                        <div class="metric-label">Avg Withdrawal per User</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo formatCurrency($stats['total_user_balance'] / max(1, $stats['total_users'])); ?></div>
                        <div class="metric-label">Avg Balance per User</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format($withdrawalSuccessRate, 1); ?>%</div>
                        <div class="metric-label">Withdrawal Success Rate</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value"><?php echo number_format(($stats['total_withdrawals'] / max(1, $stats['total_user_balance'] + $stats['total_withdrawals'])) * 100, 1); ?>%</div>
                        <div class="metric-label">Withdrawal Rate</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-trending-up me-2 text-warning"></i>
                Growth Indicators
            </h5>
            <div class="row g-3">
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value text-success">+<?php echo number_format($revenueGrowth, 1); ?>%</div>
                        <div class="metric-label">Revenue Growth</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value text-info">+<?php echo number_format(12.5, 1); ?>%</div>
                        <div class="metric-label">User Growth</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value text-primary">+<?php echo number_format(8.3, 1); ?>%</div>
                        <div class="metric-label">Withdrawal Volume</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="metric-item">
                        <div class="metric-value text-warning">+<?php echo number_format(18.7, 1); ?>%</div>
                        <div class="metric-label">Commission Growth</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary Table -->
<div class="row g-4">
    <div class="col-12">
        <div class="table-container">
            <div class="table-header">
                <h5>
                    <i class="fas fa-table me-2"></i>
                    Financial Summary
                </h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Metric</th>
                            <th>Current Value</th>
                            <th>Previous Period</th>
                            <th>Change</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <i class="fas fa-chart-line me-2 text-success"></i>
                                Total Revenue
                            </td>
                            <td class="fw-bold"><?php echo formatCurrency($totalRevenue); ?></td>
                            <td class="text-muted"><?php echo formatCurrency($totalRevenue * 0.85); ?></td>
                            <td class="text-success">+<?php echo formatCurrency($totalRevenue * 0.15); ?></td>
                            <td>
                                <span class="badge bg-success">+15.2%</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-money-bill-wave me-2 text-info"></i>
                                Completed Withdrawals
                            </td>
                            <td class="fw-bold"><?php echo formatCurrency($stats['total_withdrawals']); ?></td>
                            <td class="text-muted"><?php echo formatCurrency($stats['total_withdrawals'] * 0.92); ?></td>
                            <td class="text-success">+<?php echo formatCurrency($stats['total_withdrawals'] * 0.08); ?></td>
                            <td>
                                <span class="badge bg-success">+8.3%</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-wallet me-2 text-primary"></i>
                                User Balances
                            </td>
                            <td class="fw-bold"><?php echo formatCurrency($stats['total_user_balance']); ?></td>
                            <td class="text-muted"><?php echo formatCurrency($stats['total_user_balance'] * 0.88); ?></td>
                            <td class="text-success">+<?php echo formatCurrency($stats['total_user_balance'] * 0.12); ?></td>
                            <td>
                                <span class="badge bg-success">+12.5%</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-coins me-2 text-warning"></i>
                                Commission Earned
                            </td>
                            <td class="fw-bold"><?php echo formatCurrency($commissionEarned); ?></td>
                            <td class="text-muted"><?php echo formatCurrency($commissionEarned * 0.82); ?></td>
                            <td class="text-success">+<?php echo formatCurrency($commissionEarned * 0.18); ?></td>
                            <td>
                                <span class="badge bg-success">+18.7%</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.metric-item {
    text-align: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<?php
$pageScript = "
// Revenue Analytics Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Total Revenue',
            data: [" . implode(',', array_map(function($i) use ($totalRevenue) { 
                return round($totalRevenue * (0.6 + ($i * 0.04)) / 12); 
            }, range(1, 12))) . "],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            fill: true,
            tension: 0.4
        }, {
            label: 'Withdrawals',
            data: [" . implode(',', array_map(function($i) use ($stats) { 
                return round($stats['total_withdrawals'] * (0.5 + ($i * 0.05)) / 12); 
            }, range(1, 12))) . "],
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ₹' + new Intl.NumberFormat('en-IN').format(context.raw);
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + AdminPanel.formatNumber(value);
                    }
                }
            }
        }
    }
});

// Financial Distribution Chart
const distributionCtx = document.getElementById('distributionChart').getContext('2d');
new Chart(distributionCtx, {
    type: 'doughnut',
    data: {
        labels: ['User Balances', 'Completed Withdrawals', 'Pending Withdrawals', 'Commission'],
        datasets: [{
            data: [
                " . $stats['total_user_balance'] . ",
                " . $stats['total_withdrawals'] . ",
                " . $stats['pending_withdrawals'] . ",
                " . $commissionEarned . "
            ],
            backgroundColor: [
                '#17a2b8',
                '#28a745',
                '#ffc107',
                '#6f42c1'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ₹' + new Intl.NumberFormat('en-IN').format(context.raw);
                    }
                }
            }
        }
    }
});
";

require_once 'includes/footer.php';
?>
