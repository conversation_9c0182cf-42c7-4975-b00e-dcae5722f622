<?php
// Error log viewer for troubleshooting
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Log Viewer</title>
    <style>
        body { font-family: monospace; margin: 20px; background: #f5f5f5; }
        .log-container { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .error-line { margin: 5px 0; padding: 5px; background: #ffe6e6; border-left: 3px solid #ff0000; }
        .warning-line { margin: 5px 0; padding: 5px; background: #fff3cd; border-left: 3px solid #ffc107; }
        .info-line { margin: 5px 0; padding: 5px; background: #e6f3ff; border-left: 3px solid #007bff; }
        .timestamp { color: #666; font-weight: bold; }
        .no-errors { color: green; font-weight: bold; text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <h1>Error Log Viewer</h1>
    
    <?php
    $log_files = [
        'Admin Debug Log' => __DIR__ . '/admin_debug.log',
        'Main Bot Log' => __DIR__ . '/../data/debug.log',
        'PHP Error Log' => ini_get('error_log'),
        'Alternative Log' => __DIR__ . '/../data/admin_debug.log'
    ];
    
    $found_errors = false;
    
    foreach ($log_files as $log_name => $log_path) {
        echo "<h2>$log_name</h2>";
        echo "<div class='log-container'>";
        
        if ($log_path && file_exists($log_path) && is_readable($log_path)) {
            $log_content = file_get_contents($log_path);
            
            if (!empty(trim($log_content))) {
                $lines = explode("\n", $log_content);
                $recent_lines = array_slice($lines, -50); // Show last 50 lines
                
                foreach ($recent_lines as $line) {
                    if (empty(trim($line))) continue;
                    
                    $found_errors = true;
                    $class = 'info-line';
                    
                    if (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false) {
                        $class = 'error-line';
                    } elseif (stripos($line, 'warning') !== false || stripos($line, 'notice') !== false) {
                        $class = 'warning-line';
                    }
                    
                    // Highlight timestamp
                    $line = preg_replace('/(\[[\d\-\s:]+\])/', '<span class="timestamp">$1</span>', htmlspecialchars($line));
                    
                    echo "<div class='$class'>$line</div>";
                }
            } else {
                echo "<div class='no-errors'>No errors found in this log</div>";
            }
        } else {
            echo "<div class='info-line'>Log file not found or not readable: " . htmlspecialchars($log_path) . "</div>";
        }
        
        echo "</div><br>";
    }
    
    if (!$found_errors) {
        echo "<div class='no-errors'>";
        echo "<h3>No Error Logs Found</h3>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No errors have occurred (good!)</li>";
        echo "<li>Error logging is not configured</li>";
        echo "<li>Log files are in a different location</li>";
        echo "</ul>";
        echo "</div>";
    }
    ?>
    
    <h2>Hostinger Error Log Locations</h2>
    <div class='log-container'>
        <p>On Hostinger, error logs are typically found in:</p>
        <ul>
            <li><strong>cPanel → Error Logs</strong> (recommended)</li>
            <li><strong>File Manager → public_html → error_log</strong></li>
            <li><strong>Your domain folder → logs/</strong></li>
        </ul>
        <p>Check your Hostinger control panel for the "Error Logs" section to see detailed PHP errors.</p>
    </div>
    
    <h2>Common HTTP 500 Fixes</h2>
    <div class='log-container'>
        <ol>
            <li><strong>Fix file permissions:</strong> <a href="fix_permissions.php">Run Permission Fix</a></li>
            <li><strong>Check PHP version:</strong> Ensure PHP 7.4+ is enabled in Hostinger</li>
            <li><strong>Clear cache:</strong> Delete files in the cache/ directory</li>
            <li><strong>Check .htaccess:</strong> Temporarily rename .htaccess to .htaccess-backup</li>
            <li><strong>Increase memory:</strong> Add ini_set('memory_limit', '256M'); to config.php</li>
        </ol>
    </div>
    
    <p><a href="test.php">← Back to Test Page</a></p>
</body>
</html>
