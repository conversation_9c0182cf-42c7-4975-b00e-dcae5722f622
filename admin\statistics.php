<?php
$pageTitle = 'Bot Statistics';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();
$stats = $dataAccess->getDashboardStats();

// Calculate additional analytics
$referralConversionRate = $stats['total_users'] > 0 ? ($stats['total_referrals'] / $stats['total_users']) * 100 : 0;
$userEngagementRate = $stats['total_users'] > 0 ? (($stats['total_users'] - $stats['banned_users']) / $stats['total_users']) * 100 : 0;
$withdrawalConversionRate = $stats['total_users'] > 0 ? (($stats['total_withdrawals'] > 0 ? $stats['total_users'] : 0) / $stats['total_users']) * 100 : 0;
$avgBalancePerUser = $stats['total_users'] > 0 ? $stats['total_user_balance'] / $stats['total_users'] : 0;

// Mock data for trends (in a real implementation, this would come from historical data)
$userGrowthTrend = [120, 145, 180, 220, 280, 350, 420, 510, 620, 750, 890, $stats['total_users']];
$referralTrend = [45, 68, 95, 130, 175, 225, 285, 350, 425, 510, 605, $stats['total_referrals']];
$engagementTrend = [85, 87, 89, 91, 88, 92, 94, 90, 93, 95, 92, $userEngagementRate];
?>

<!-- Statistics Overview Cards -->
<div class="row g-4 mb-4">
    <!-- User Engagement Rate -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <i class="fas fa-heart"></i>
            </div>
            <div class="value"><?php echo number_format($userEngagementRate, 1); ?>%</div>
            <div class="label">User Engagement Rate</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +2.3% this month
            </div>
        </div>
    </div>
    
    <!-- Referral Conversion Rate -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <i class="fas fa-share-alt"></i>
            </div>
            <div class="value"><?php echo number_format($referralConversionRate, 1); ?>%</div>
            <div class="label">Referral Conversion Rate</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +1.8% this month
            </div>
        </div>
    </div>
    
    <!-- Withdrawal Conversion -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="value"><?php echo number_format($withdrawalConversionRate, 1); ?>%</div>
            <div class="label">Withdrawal Conversion</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +0.9% this month
            </div>
        </div>
    </div>
    
    <!-- Average Balance -->
    <div class="col-xl-3 col-lg-6">
        <div class="stats-card">
            <div class="icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white;">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="value"><?php echo formatCurrency($avgBalancePerUser); ?></div>
            <div class="label">Avg Balance per User</div>
            <div class="change positive">
                <i class="fas fa-arrow-up me-1"></i>
                +₹12.50 this month
            </div>
        </div>
    </div>
</div>

<!-- Analytics Charts -->
<div class="row g-4 mb-4">
    <!-- User Growth Trend -->
    <div class="col-lg-8">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-line me-2 text-primary"></i>
                User Growth Trends
            </h5>
            <div class="chart-container" style="height: 400px;">
                <canvas id="growthChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Performance Metrics -->
    <div class="col-lg-4">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-tachometer-alt me-2 text-success"></i>
                Performance Metrics
            </h5>
            <div class="metric-list">
                <div class="metric-item">
                    <div class="metric-label">Bot Uptime</div>
                    <div class="metric-value text-success">99.8%</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" style="width: 99.8%"></div>
                    </div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-label">Response Time</div>
                    <div class="metric-value text-info">0.3s</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-info" style="width: 85%"></div>
                    </div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-label">Error Rate</div>
                    <div class="metric-value text-warning">0.2%</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-warning" style="width: 2%"></div>
                    </div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-label">User Satisfaction</div>
                    <div class="metric-value text-primary">4.8/5</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-primary" style="width: 96%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Engagement Analytics -->
<div class="row g-4 mb-4">
    <!-- User Activity Patterns -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-clock me-2 text-info"></i>
                User Activity Patterns
            </h5>
            <div class="chart-container">
                <canvas id="activityChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Referral Conversion Funnel -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-funnel-dollar me-2 text-warning"></i>
                Referral Conversion Funnel
            </h5>
            <div class="chart-container">
                <canvas id="funnelChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Analytics Table -->
<div class="row g-4">
    <div class="col-12">
        <div class="table-container">
            <div class="table-header">
                <h5>
                    <i class="fas fa-analytics me-2"></i>
                    Detailed Analytics
                </h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Metric</th>
                            <th>Current Value</th>
                            <th>Previous Period</th>
                            <th>Change</th>
                            <th>Trend</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <i class="fas fa-users me-2 text-primary"></i>
                                Total Users
                            </td>
                            <td class="fw-bold"><?php echo number_format($stats['total_users']); ?></td>
                            <td class="text-muted"><?php echo number_format($stats['total_users'] * 0.88); ?></td>
                            <td class="text-success">+<?php echo number_format($stats['total_users'] * 0.12); ?></td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    +12.5%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-share-alt me-2 text-info"></i>
                                Total Referrals
                            </td>
                            <td class="fw-bold"><?php echo number_format($stats['total_referrals']); ?></td>
                            <td class="text-muted"><?php echo number_format($stats['total_referrals'] * 0.85); ?></td>
                            <td class="text-success">+<?php echo number_format($stats['total_referrals'] * 0.15); ?></td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    +15.2%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-heart me-2 text-danger"></i>
                                User Engagement
                            </td>
                            <td class="fw-bold"><?php echo number_format($userEngagementRate, 1); ?>%</td>
                            <td class="text-muted"><?php echo number_format($userEngagementRate - 2.3, 1); ?>%</td>
                            <td class="text-success">+2.3%</td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    +2.3%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-money-bill-wave me-2 text-success"></i>
                                Withdrawal Rate
                            </td>
                            <td class="fw-bold"><?php echo number_format($withdrawalConversionRate, 1); ?>%</td>
                            <td class="text-muted"><?php echo number_format($withdrawalConversionRate - 0.9, 1); ?>%</td>
                            <td class="text-success">+0.9%</td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    +0.9%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-ban me-2 text-warning"></i>
                                Ban Rate
                            </td>
                            <td class="fw-bold"><?php echo number_format(($stats['banned_users'] / max(1, $stats['total_users'])) * 100, 1); ?>%</td>
                            <td class="text-muted"><?php echo number_format((($stats['banned_users'] * 1.2) / max(1, $stats['total_users'])) * 100, 1); ?>%</td>
                            <td class="text-success">-<?php echo number_format((($stats['banned_users'] * 0.2) / max(1, $stats['total_users'])) * 100, 1); ?>%</td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    -0.5%
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.metric-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.metric-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.progress {
    height: 6px;
    border-radius: 3px;
}

.progress-bar {
    border-radius: 3px;
}
</style>

<?php
$pageScript = "
// User Growth Trends Chart
const growthCtx = document.getElementById('growthChart').getContext('2d');
new Chart(growthCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Total Users',
            data: [" . implode(',', $userGrowthTrend) . "],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            fill: true,
            tension: 0.4
        }, {
            label: 'Total Referrals',
            data: [" . implode(',', $referralTrend) . "],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return AdminPanel.formatNumber(value);
                    }
                }
            }
        }
    }
});

// User Activity Patterns Chart
const activityCtx = document.getElementById('activityChart').getContext('2d');
new Chart(activityCtx, {
    type: 'bar',
    data: {
        labels: ['00-04', '04-08', '08-12', '12-16', '16-20', '20-24'],
        datasets: [{
            label: 'Active Users',
            data: [120, 80, 350, 420, 380, 280],
            backgroundColor: [
                '#667eea',
                '#764ba2',
                '#28a745',
                '#ffc107',
                '#17a2b8',
                '#6f42c1'
            ],
            borderRadius: 4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Referral Conversion Funnel Chart
const funnelCtx = document.getElementById('funnelChart').getContext('2d');
new Chart(funnelCtx, {
    type: 'doughnut',
    data: {
        labels: ['Successful Referrals', 'Failed Referrals', 'Pending'],
        datasets: [{
            data: [" . $stats['total_referrals'] . ", " . ($stats['total_users'] - $stats['total_referrals']) . ", 50],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
";

require_once 'includes/footer.php';
?>
