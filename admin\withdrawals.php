<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load data
$users = [];
$withdrawal_reports = [];
$error = '';
$action_message = '';

try {
    // Load users
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
        }
    }
    
    // Load withdrawal reports
    $withdrawals_file = __DIR__ . '/../data/withdrawal_reports.json';
    if (file_exists($withdrawals_file)) {
        $content = file_get_contents($withdrawals_file);
        if ($content) {
            $withdrawal_reports = json_decode($content, true);
            if (!is_array($withdrawal_reports)) {
                $withdrawal_reports = [];
            }
        }
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Handle withdrawal actions
if ($_POST && isset($_POST['action']) && isset($_POST['withdrawal_id'])) {
    $withdrawal_id = $_POST['withdrawal_id'];
    $action = $_POST['action'];
    
    if (isset($withdrawal_reports[$withdrawal_id])) {
        $withdrawal = &$withdrawal_reports[$withdrawal_id];
        $user_id = $withdrawal['user_id'];
        
        if ($action === 'approve' && $withdrawal['status'] === 'Under review') {
            // Approve withdrawal
            $withdrawal['status'] = 'Completed';
            $withdrawal['processed_at'] = date('Y-m-d H:i:s');
            
            // Update user's successful withdrawal amount
            if (isset($users[$user_id])) {
                $users[$user_id]['successful_withdraw'] = ($users[$user_id]['successful_withdraw'] ?? 0) + $withdrawal['amount'];
                $users[$user_id]['withdraw_under_review'] = max(0, ($users[$user_id]['withdraw_under_review'] ?? 0) - $withdrawal['amount']);
            }
            
            $action_message = "Withdrawal of ₹{$withdrawal['amount']} approved for user {$user_id}";
            
        } elseif ($action === 'reject' && $withdrawal['status'] === 'Under review') {
            // Reject withdrawal
            $withdrawal['status'] = 'Rejected';
            $withdrawal['processed_at'] = date('Y-m-d H:i:s');
            $withdrawal['rejection_reason'] = $_POST['reason'] ?? 'No reason provided';
            
            // Return amount to user's balance
            if (isset($users[$user_id])) {
                $users[$user_id]['balance'] = ($users[$user_id]['balance'] ?? 0) + $withdrawal['amount'];
                $users[$user_id]['withdraw_under_review'] = max(0, ($users[$user_id]['withdraw_under_review'] ?? 0) - $withdrawal['amount']);
            }
            
            $action_message = "Withdrawal of ₹{$withdrawal['amount']} rejected for user {$user_id}";
        }
        
        // Save changes
        try {
            file_put_contents($withdrawals_file, json_encode($withdrawal_reports, JSON_PRETTY_PRINT));
            file_put_contents($users_file, json_encode($users, JSON_PRETTY_PRINT));
        } catch (Exception $e) {
            $action_message = "Error saving changes: " . $e->getMessage();
        }
    }
}

// Filter withdrawals
$filter = $_GET['filter'] ?? 'pending';
$filtered_withdrawals = [];

foreach ($withdrawal_reports as $id => $withdrawal) {
    switch ($filter) {
        case 'pending':
            if ($withdrawal['status'] !== 'Under review') continue 2;
            break;
        case 'completed':
            if ($withdrawal['status'] !== 'Completed') continue 2;
            break;
        case 'rejected':
            if ($withdrawal['status'] !== 'Rejected') continue 2;
            break;
    }
    
    $withdrawal['id'] = $id;
    $withdrawal['user_name'] = $users[$withdrawal['user_id']]['first_name'] ?? 'Unknown';
    $filtered_withdrawals[] = $withdrawal;
}

// Sort by date (newest first)
usort($filtered_withdrawals, function($a, $b) {
    return strtotime($b['date'] ?? '0') - strtotime($a['date'] ?? '0');
});

// Calculate statistics
$stats = [
    'pending_count' => 0,
    'pending_amount' => 0,
    'completed_count' => 0,
    'completed_amount' => 0,
    'rejected_count' => 0,
    'rejected_amount' => 0,
    'total_amount' => 0
];

foreach ($withdrawal_reports as $withdrawal) {
    $amount = $withdrawal['amount'] ?? 0;
    $stats['total_amount'] += $amount;
    
    switch ($withdrawal['status']) {
        case 'Under review':
            $stats['pending_count']++;
            $stats['pending_amount'] += $amount;
            break;
        case 'Completed':
            $stats['completed_count']++;
            $stats['completed_amount'] += $amount;
            break;
        case 'Rejected':
            $stats['rejected_count']++;
            $stats['rejected_amount'] += $amount;
            break;
    }
}

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$total_withdrawals = count($filtered_withdrawals);
$total_pages = ceil($total_withdrawals / $per_page);
$offset = ($page - 1) * $per_page;
$withdrawals_page = array_slice($filtered_withdrawals, $offset, $per_page);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Withdrawal Management</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 1.8em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .pending { color: #ffc107; }
        .completed { color: #28a745; }
        .rejected { color: #dc3545; }
        .total { color: #007bff; }
        .controls { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .filter-tabs { display: flex; gap: 10px; margin-bottom: 20px; }
        .filter-tab { padding: 10px 20px; background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 5px; text-decoration: none; color: #495057; }
        .filter-tab.active { background: #007bff; color: white; border-color: #007bff; }
        .withdrawals-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; }
        tr:hover { background: #f8f9fa; }
        .status-badge { padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .action-buttons { display: flex; gap: 5px; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 12px; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; }
        .pagination { text-align: center; margin: 20px 0; }
        .pagination a { display: inline-block; padding: 8px 12px; margin: 0 2px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .pagination a:hover { background: #0056b3; }
        .pagination .current { background: #28a745; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.success { background: #d4edda; color: #155724; }
        .message.error { background: #f8d7da; color: #721c24; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 15% auto; padding: 20px; border-radius: 10px; width: 400px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .close { font-size: 28px; font-weight: bold; cursor: pointer; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box; }
    </style>
</head>
<body>
    <div class="header">
        <h1>💰 Withdrawal Management</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($action_message): ?>
            <div class="message success">✅ <?php echo htmlspecialchars($action_message); ?></div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number pending"><?php echo $stats['pending_count']; ?></div>
                <div class="stat-label">Pending Requests</div>
                <div style="font-size: 0.8em; color: #ffc107; font-weight: bold;">₹<?php echo number_format($stats['pending_amount'], 2); ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-number completed"><?php echo $stats['completed_count']; ?></div>
                <div class="stat-label">Completed</div>
                <div style="font-size: 0.8em; color: #28a745; font-weight: bold;">₹<?php echo number_format($stats['completed_amount'], 2); ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-number rejected"><?php echo $stats['rejected_count']; ?></div>
                <div class="stat-label">Rejected</div>
                <div style="font-size: 0.8em; color: #dc3545; font-weight: bold;">₹<?php echo number_format($stats['rejected_amount'], 2); ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-number total"><?php echo count($withdrawal_reports); ?></div>
                <div class="stat-label">Total Requests</div>
                <div style="font-size: 0.8em; color: #007bff; font-weight: bold;">₹<?php echo number_format($stats['total_amount'], 2); ?></div>
            </div>
        </div>
        
        <!-- Filter Tabs -->
        <div class="controls">
            <div class="filter-tabs">
                <a href="?filter=pending" class="filter-tab <?php echo $filter === 'pending' ? 'active' : ''; ?>">
                    ⏳ Pending (<?php echo $stats['pending_count']; ?>)
                </a>
                <a href="?filter=completed" class="filter-tab <?php echo $filter === 'completed' ? 'active' : ''; ?>">
                    ✅ Completed (<?php echo $stats['completed_count']; ?>)
                </a>
                <a href="?filter=rejected" class="filter-tab <?php echo $filter === 'rejected' ? 'active' : ''; ?>">
                    ❌ Rejected (<?php echo $stats['rejected_count']; ?>)
                </a>
                <a href="?filter=all" class="filter-tab <?php echo $filter === 'all' ? 'active' : ''; ?>">
                    📋 All (<?php echo count($withdrawal_reports); ?>)
                </a>
            </div>
        </div>
        
        <!-- Withdrawals Table -->
        <?php if (!empty($withdrawals_page)): ?>
            <div class="withdrawals-table">
                <table>
                    <thead>
                        <tr>
                            <th>Request ID</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($withdrawals_page as $withdrawal): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($withdrawal['id']); ?></strong></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($withdrawal['user_name']); ?></strong><br>
                                    <small>ID: <?php echo htmlspecialchars($withdrawal['user_id']); ?></small>
                                </td>
                                <td>
                                    <strong style="color: #28a745;">₹<?php echo number_format($withdrawal['amount'], 2); ?></strong>
                                </td>
                                <td>
                                    <?php echo strtoupper($withdrawal['method'] ?? 'Bank'); ?><br>
                                    <?php if (isset($withdrawal['account_details'])): ?>
                                        <small style="color: #666;"><?php echo htmlspecialchars(substr($withdrawal['account_details'], 0, 20)); ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo date('M d, Y', strtotime($withdrawal['date'])); ?><br>
                                    <small><?php echo date('H:i', strtotime($withdrawal['date'])); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $status_class = 'status-pending';
                                    $status_icon = '⏳';
                                    if ($withdrawal['status'] === 'Completed') {
                                        $status_class = 'status-completed';
                                        $status_icon = '✅';
                                    } elseif ($withdrawal['status'] === 'Rejected') {
                                        $status_class = 'status-rejected';
                                        $status_icon = '❌';
                                    }
                                    ?>
                                    <span class="status-badge <?php echo $status_class; ?>">
                                        <?php echo $status_icon; ?> <?php echo $withdrawal['status']; ?>
                                    </span>
                                    <?php if (isset($withdrawal['processed_at'])): ?>
                                        <br><small>Processed: <?php echo date('M d, H:i', strtotime($withdrawal['processed_at'])); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button onclick="viewDetails('<?php echo $withdrawal['id']; ?>')" class="btn btn-info">👁️ View</button>
                                        
                                        <?php if ($withdrawal['status'] === 'Under review'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="withdrawal_id" value="<?php echo $withdrawal['id']; ?>">
                                                <input type="hidden" name="action" value="approve">
                                                <button type="submit" class="btn btn-success" onclick="return confirm('Approve this withdrawal?')">✅ Approve</button>
                                            </form>
                                            
                                            <button onclick="showRejectModal('<?php echo $withdrawal['id']; ?>')" class="btn btn-danger">❌ Reject</button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>">← Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>" 
                           <?php echo $i === $page ? 'class="current"' : ''; ?>>
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>">Next →</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="message">No withdrawal requests found for the selected filter.</div>
        <?php endif; ?>
    </div>
    
    <!-- Reject Modal -->
    <div id="rejectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Reject Withdrawal</h3>
                <span class="close" onclick="closeRejectModal()">&times;</span>
            </div>
            <form method="POST">
                <input type="hidden" name="withdrawal_id" id="rejectWithdrawalId">
                <input type="hidden" name="action" value="reject">
                
                <div class="form-group">
                    <label>Rejection Reason:</label>
                    <textarea name="reason" rows="4" placeholder="Enter reason for rejection..." required></textarea>
                </div>
                
                <div style="text-align: right;">
                    <button type="button" onclick="closeRejectModal()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px;">Cancel</button>
                    <button type="submit" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Reject Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showRejectModal(withdrawalId) {
            document.getElementById('rejectWithdrawalId').value = withdrawalId;
            document.getElementById('rejectModal').style.display = 'block';
        }
        
        function closeRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
        }
        
        function viewDetails(withdrawalId) {
            alert('Detailed view for withdrawal ' + withdrawalId + ' - Feature coming soon!');
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('rejectModal');
            if (event.target === modal) {
                closeRejectModal();
            }
        }
    </script>
</body>
</html>
