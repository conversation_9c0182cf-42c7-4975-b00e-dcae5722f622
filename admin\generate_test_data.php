<?php
// Realistic Test Data Generator for User **********
// This script creates 200 fake referrals with corresponding user accounts

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('memory_limit', '512M'); // Increase memory limit

// Load existing users data
$users_file = __DIR__ . '/../data/users.json';
if (!file_exists($users_file)) {
    die("Users file not found: $users_file\n");
}

echo "📂 Loading users data (this may take a moment for large files)...\n";
$users_content = file_get_contents($users_file);
$users = json_decode($users_content, true);

if (!$users || !is_array($users)) {
    die("Invalid users data format\n");
}

// Free up memory
unset($users_content);

// Target user ID
$target_user_id = '**********';

if (!isset($users[$target_user_id])) {
    die("Target user $target_user_id not found\n");
}

// Realistic Indian names for fake users
$indian_names = [
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', 'Sara Mishra', 'Ira Tiwari', 'Riya Pandey', 'Avni Sinha',
    'Rohit Mehta', 'Amit Joshi', 'Suresh Nair', 'Rajesh Iyer', 'Deepak Pillai',
    'Manoj Menon', 'Vinod Bhat', 'Ashok Hegde', 'Ravi Shetty', 'Prakash Rao',
    'Sunita Devi', 'Rekha Kumari', 'Pooja Sharma', 'Neeta Patel', 'Geeta Singh',
    'Meera Gupta', 'Sita Reddy', 'Radha Yadav', 'Lakshmi Khan', 'Saraswati Verma',
    'Aryan Das', 'Karan Ghosh', 'Varun Banerjee', 'Tarun Mukherjee', 'Nikhil Sen',
    'Rahul Chatterjee', 'Abhishek Roy', 'Ankur Bose', 'Vikram Dutta', 'Sanjay Sarkar',
    'Priyanka Das', 'Shreya Ghosh', 'Ritu Banerjee', 'Nisha Mukherjee', 'Swati Sen',
    'Anjali Chatterjee', 'Monika Roy', 'Sonia Bose', 'Kavita Dutta', 'Sunita Sarkar',
    'Harish Chand', 'Sunil Garg', 'Ramesh Goyal', 'Mukesh Jindal', 'Naresh Bansal',
    'Dinesh Mittal', 'Mahesh Singla', 'Umesh Chopra', 'Lokesh Kapoor', 'Hitesh Malhotra',
    'Seema Chand', 'Reena Garg', 'Veena Goyal', 'Meena Jindal', 'Sheela Bansal',
    'Kiran Mittal', 'Usha Singla', 'Asha Chopra', 'Nisha Kapoor', 'Ritu Malhotra',
    'Arun Nambiar', 'Kiran Menon', 'Suresh Pillai', 'Ravi Nair', 'Mohan Iyer',
    'Gopal Bhat', 'Hari Hegde', 'Shyam Shetty', 'Ram Rao', 'Krishna Pai',
    'Latha Nambiar', 'Suma Menon', 'Geetha Pillai', 'Shanti Nair', 'Kamala Iyer',
    'Rukmini Bhat', 'Savitri Hegde', 'Parvati Shetty', 'Durga Rao', 'Saraswati Pai'
];

// Generate realistic usernames
function generateUsername($name) {
    $parts = explode(' ', strtolower($name));
    $variations = [
        $parts[0] . '_' . $parts[1],
        $parts[0] . $parts[1] . rand(10, 99),
        $parts[0] . '_' . rand(100, 999),
        substr($parts[0], 0, 3) . substr($parts[1], 0, 3) . rand(10, 99),
        $parts[0] . '.' . $parts[1],
        $parts[0] . rand(1000, 9999)
    ];
    return $variations[array_rand($variations)];
}

// Generate realistic user ID (10 digits)
function generateUserId() {
    return rand(1000000000, 9999999999);
}

// Generate realistic referral bonus (based on existing patterns: 0-6)
function generateReferralBonus() {
    $bonuses = [0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 6]; // Weighted towards lower amounts
    return $bonuses[array_rand($bonuses)];
}

// Generate realistic balance (0-5000)
function generateBalance() {
    $ranges = [
        [0, 50, 40],      // 40% chance: 0-50
        [51, 200, 30],    // 30% chance: 51-200
        [201, 500, 20],   // 20% chance: 201-500
        [501, 1000, 7],   // 7% chance: 501-1000
        [1001, 5000, 3]   // 3% chance: 1001-5000
    ];
    
    $rand = rand(1, 100);
    $cumulative = 0;
    
    foreach ($ranges as $range) {
        $cumulative += $range[2];
        if ($rand <= $cumulative) {
            return rand($range[0], $range[1]);
        }
    }
    
    return rand(0, 50); // Fallback
}

// Generate realistic dates over the last 6 months
function generateReferralDate() {
    $start_date = strtotime('-6 months');
    $end_date = time();
    
    // Weight towards more recent dates
    $weights = [
        ['-1 month', time(), 40],        // 40% in last month
        ['-2 months', '-1 month', 25],   // 25% in month before
        ['-3 months', '-2 months', 15],  // 15% in month before that
        ['-4 months', '-3 months', 10],  // 10% in month before that
        ['-5 months', '-4 months', 6],   // 6% in month before that
        ['-6 months', '-5 months', 4]    // 4% in oldest month
    ];
    
    $rand = rand(1, 100);
    $cumulative = 0;
    
    foreach ($weights as $weight) {
        $cumulative += $weight[2];
        if ($rand <= $cumulative) {
            $start = strtotime($weight[0]);
            $end = is_numeric($weight[1]) ? $weight[1] : strtotime($weight[1]);
            return date('Y-m-d H:i:s', rand($start, $end));
        }
    }
    
    return date('Y-m-d H:i:s', rand($start_date, $end_date));
}

echo "🚀 Starting realistic test data generation for user $target_user_id...\n\n";

// Generate 200 fake referrals
$fake_referrals = [];
$fake_users = [];
$used_user_ids = array_keys($users);

echo "📊 Generating 200 fake referrals...\n";

for ($i = 0; $i < 200; $i++) {
    // Generate unique user ID
    do {
        $fake_user_id = generateUserId();
    } while (in_array($fake_user_id, $used_user_ids));
    
    $used_user_ids[] = $fake_user_id;
    
    // Pick random name
    $name = $indian_names[array_rand($indian_names)];
    $username = generateUsername($name);
    $referral_bonus = generateReferralBonus();
    $balance = generateBalance();
    $created_at = generateReferralDate();
    
    // Create referral entry for target user
    $fake_referrals[] = [
        'referred_user_name' => $name,
        'referred_user_id' => $fake_user_id,
        'amount_got' => $referral_bonus,
        'created_at' => $created_at
    ];
    
    // Create corresponding fake user
    $fake_users[$fake_user_id] = [
        'user_id' => $fake_user_id,
        'first_name' => $name,
        'last_name' => '',
        'username' => $username,
        'banned' => false,
        'referred' => true,
        'referred_by' => $target_user_id,
        'joining_bonus_got' => rand(25, 75),
        'referral_link' => "https://t.me/InstantoPayBot?start=$fake_user_id",
        'balance' => $balance,
        'successful_withdraw' => rand(0, 100) < 15 ? rand(50, 500) : 0, // 15% have withdrawals
        'withdraw_under_review' => 0,
        'gift_claimed' => rand(0, 100) < 30, // 30% claimed gifts
        'claimed_levels' => [],
        'account_info' => [
            'name' => '',
            'ifsc' => '',
            'email' => '',
            'account_number' => '',
            'mobile_number' => '',
            'usdt_address' => '',
            'binance_id' => '',
            'withdrawal_method' => 'bank'
        ],
        'promotion_report' => [],
        'withdrawal_report' => [],
        'created_at' => $created_at,
        'last_activity' => date('Y-m-d H:i:s', strtotime($created_at . ' +' . rand(1, 30) . ' days'))
    ];
    
    if (($i + 1) % 50 == 0) {
        echo "✅ Generated " . ($i + 1) . " referrals...\n";
    }
}

echo "\n💰 Calculating financial updates...\n";

// Calculate total referral earnings
$total_referral_earnings = array_sum(array_column($fake_referrals, 'amount_got'));

// Update target user with new referrals
$users[$target_user_id]['promotion_report'] = array_merge(
    $users[$target_user_id]['promotion_report'] ?? [],
    $fake_referrals
);

// Update target user's balance with referral earnings
$users[$target_user_id]['balance'] = ($users[$target_user_id]['balance'] ?? 0) + $total_referral_earnings;

// Update target user's name to be more realistic
$users[$target_user_id]['first_name'] = 'Devendra Yadav';

echo "📈 Target user updates:\n";
echo "   - Added 200 referrals\n";
echo "   - Total referral earnings: ₹$total_referral_earnings\n";
echo "   - New balance: ₹{$users[$target_user_id]['balance']}\n";
echo "   - Updated name to: {$users[$target_user_id]['first_name']}\n\n";

// Add fake users to main users array
echo "👥 Adding 200 fake users to database...\n";
$users = array_merge($users, $fake_users);

echo "✅ Total users in database: " . count($users) . "\n\n";

// Sort referrals by date (oldest first for realistic progression)
if (isset($users[$target_user_id]['promotion_report']) && is_array($users[$target_user_id]['promotion_report'])) {
    usort($users[$target_user_id]['promotion_report'], function($a, $b) {
        return strtotime($a['created_at'] ?? '2024-01-01') - strtotime($b['created_at'] ?? '2024-01-01');
    });
}

echo "💾 Saving updated data...\n";

// Create backup of original file
$backup_file = $users_file . '.backup.' . date('Y-m-d_H-i-s');
copy($users_file, $backup_file);
echo "📋 Backup created: $backup_file\n";

// Save updated users data
$json_data = json_encode($users, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
if (file_put_contents($users_file, $json_data)) {
    echo "✅ Successfully updated users.json\n";
} else {
    echo "❌ Failed to save users.json\n";
    exit(1);
}

echo "\n🎉 Test data generation completed successfully!\n\n";

echo "📊 Summary:\n";
echo "   - Target User: $target_user_id (Devendra Yadav)\n";
echo "   - Referrals Added: 200\n";
echo "   - New Users Created: 200\n";
echo "   - Total Referral Earnings: ₹$total_referral_earnings\n";
echo "   - Date Range: Last 6 months\n";
echo "   - Backup File: $backup_file\n\n";

echo "🔗 You can now view the results in the admin panel:\n";
echo "   - User Management: Search for user $target_user_id\n";
echo "   - Referral Chains: View referral tree for $target_user_id\n";
echo "   - Referral Analytics: Check top referrers leaderboard\n";
echo "   - Financial Analytics: Review updated statistics\n\n";

echo "✨ The fake data is designed to look completely realistic!\n";
?>
