<?php
// Simple test page to verify admin panel functionality
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Admin Panel Test Page</h1>
    <p>If you can see this page, P<PERSON> is working correctly.</p>
    
    <h2>Basic PHP Information</h2>
    <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
    <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
    <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <h2>File System Test</h2>
    <?php
    $test_results = [];
    
    // Test file existence
    $files_to_test = ['config.php', 'login.php', 'dashboard.php'];
    foreach ($files_to_test as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            $test_results[] = "<span class='success'>✓ $file exists</span>";
        } else {
            $test_results[] = "<span class='error'>✗ $file missing</span>";
        }
    }
    
    // Test directory permissions
    $cache_dir = __DIR__ . '/cache';
    if (!is_dir($cache_dir)) {
        if (mkdir($cache_dir, 0755, true)) {
            $test_results[] = "<span class='success'>✓ Created cache directory</span>";
        } else {
            $test_results[] = "<span class='error'>✗ Cannot create cache directory</span>";
        }
    } else {
        $test_results[] = "<span class='success'>✓ Cache directory exists</span>";
    }
    
    if (is_writable($cache_dir)) {
        $test_results[] = "<span class='success'>✓ Cache directory is writable</span>";
    } else {
        $test_results[] = "<span class='error'>✗ Cache directory is not writable</span>";
    }
    
    foreach ($test_results as $result) {
        echo "<p>$result</p>";
    }
    ?>
    
    <h2>Configuration Test</h2>
    <?php
    try {
        if (file_exists(__DIR__ . '/config.php')) {
            echo "<p class='success'>✓ Config file exists</p>";
            
            // Test including config
            ob_start();
            include_once __DIR__ . '/config.php';
            $config_output = ob_get_clean();
            
            if (defined('ADMIN_PASSCODE')) {
                echo "<p class='success'>✓ Admin passcode is configured</p>";
            } else {
                echo "<p class='error'>✗ Admin passcode not configured</p>";
            }
            
            if (defined('STORAGE_MODE')) {
                echo "<p class='info'>ℹ Storage mode: " . STORAGE_MODE . "</p>";
            } else {
                echo "<p class='error'>✗ Storage mode not defined</p>";
            }
            
        } else {
            echo "<p class='error'>✗ Config file not found</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error loading config: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    ?>
    
    <h2>Session Test</h2>
    <?php
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p class='success'>✓ Sessions are working</p>";
        $_SESSION['test'] = 'Admin panel test';
        echo "<p class='info'>ℹ Test session variable set</p>";
    } else {
        echo "<p class='error'>✗ Sessions not working</p>";
    }
    ?>
    
    <h2>Next Steps</h2>
    <p>If all tests above show green checkmarks (✓), try accessing:</p>
    <ul>
        <li><a href="login.php">Login Page</a></li>
        <li><a href="index.php">Admin Panel Index</a></li>
    </ul>
    
    <p>If you see any red X marks (✗), those issues need to be fixed first.</p>
    
    <hr>
    <p><small>Admin Panel Test - <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
