<?php
$pageTitle = 'Settings';
require_once 'includes/header.php';

// Get current settings
$storageMode = STORAGE_MODE;
$cacheEnabled = CACHE_ENABLED;
$defaultPageSize = DEFAULT_PAGE_SIZE;
$maxPageSize = MAX_PAGE_SIZE;
$adminVersion = ADMIN_PANEL_VERSION;
?>

<!-- Settings Overview -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="stats-card">
            <h2 class="mb-3">
                <i class="fas fa-cog me-3 text-primary"></i>
                Admin Panel Settings
            </h2>
            <p class="text-muted mb-0">
                Configure admin panel preferences and system settings
            </p>
        </div>
    </div>
</div>

<!-- Settings Sections -->
<div class="row g-4">
    <!-- System Information -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2 text-info"></i>
                System Information
            </h5>
            <div class="settings-list">
                <div class="setting-item">
                    <div class="setting-label">Admin Panel Version</div>
                    <div class="setting-value">
                        <span class="badge bg-primary"><?php echo $adminVersion; ?></span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Storage Mode</div>
                    <div class="setting-value">
                        <span class="badge bg-<?php echo $storageMode === 'mysql' ? 'success' : 'info'; ?>">
                            <?php echo strtoupper($storageMode); ?>
                        </span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Cache Status</div>
                    <div class="setting-value">
                        <span class="badge bg-<?php echo $cacheEnabled ? 'success' : 'warning'; ?>">
                            <i class="fas fa-<?php echo $cacheEnabled ? 'check' : 'times'; ?> me-1"></i>
                            <?php echo $cacheEnabled ? 'Enabled' : 'Disabled'; ?>
                        </span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">PHP Version</div>
                    <div class="setting-value">
                        <span class="badge bg-secondary"><?php echo PHP_VERSION; ?></span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Memory Limit</div>
                    <div class="setting-value">
                        <span class="badge bg-secondary"><?php echo ini_get('memory_limit'); ?></span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Timezone</div>
                    <div class="setting-value">
                        <span class="badge bg-secondary"><?php echo date_default_timezone_get(); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Performance Settings -->
    <div class="col-lg-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-tachometer-alt me-2 text-success"></i>
                Performance Settings
            </h5>
            <div class="settings-list">
                <div class="setting-item">
                    <div class="setting-label">Default Page Size</div>
                    <div class="setting-value">
                        <span class="badge bg-primary"><?php echo $defaultPageSize; ?> records</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Maximum Page Size</div>
                    <div class="setting-value">
                        <span class="badge bg-warning"><?php echo $maxPageSize; ?> records</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Cache Duration</div>
                    <div class="setting-value">
                        <span class="badge bg-info"><?php echo CACHE_DURATION; ?> seconds</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Query Timeout</div>
                    <div class="setting-value">
                        <span class="badge bg-secondary"><?php echo QUERY_TIMEOUT; ?> seconds</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">Session Timeout</div>
                    <div class="setting-value">
                        <span class="badge bg-secondary"><?php echo ADMIN_SESSION_TIMEOUT / 60; ?> minutes</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cache Management -->
<div class="row g-4 mt-4">
    <div class="col-lg-8">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-database me-2 text-warning"></i>
                Cache Management
            </h5>
            <p class="text-muted mb-3">
                Manage system cache to improve performance and ensure data freshness.
            </p>
            
            <div class="row g-3">
                <div class="col-md-4">
                    <button class="btn btn-outline-warning w-100" onclick="clearCache('stats')">
                        <i class="fas fa-chart-bar me-2"></i>
                        Clear Stats Cache
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-info w-100" onclick="clearCache('leaderboards')">
                        <i class="fas fa-trophy me-2"></i>
                        Clear Leaderboards
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger w-100" onclick="clearCache('all')">
                        <i class="fas fa-trash me-2"></i>
                        Clear All Cache
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-tools me-2 text-primary"></i>
                Quick Actions
            </h5>
            <div class="d-grid gap-2">
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-2"></i>
                    Refresh All Data
                </button>
                <button class="btn btn-outline-success" onclick="exportData()">
                    <i class="fas fa-download me-2"></i>
                    Export Statistics
                </button>
                <button class="btn btn-outline-info" onclick="viewLogs()">
                    <i class="fas fa-file-alt me-2"></i>
                    View System Logs
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Security Information -->
<div class="row g-4 mt-4">
    <div class="col-12">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-shield-alt me-2 text-danger"></i>
                Security Information
            </h5>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Security Notice:</strong> This admin panel is protected by passcode authentication. 
                Always log out when finished and never share your access credentials.
            </div>
            
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="security-item">
                        <i class="fas fa-lock text-success me-2"></i>
                        <strong>Session Security:</strong> Auto-logout after <?php echo ADMIN_SESSION_TIMEOUT / 60; ?> minutes of inactivity
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="security-item">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        <strong>Data Protection:</strong> All sensitive data is encrypted and secured
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="security-item">
                        <i class="fas fa-eye-slash text-success me-2"></i>
                        <strong>Privacy:</strong> User data is handled according to privacy policies
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="security-item">
                        <i class="fas fa-history text-success me-2"></i>
                        <strong>Audit Trail:</strong> All admin actions are logged for security
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.setting-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.setting-label {
    font-weight: 500;
    color: var(--dark-color);
    flex: 1;
}

.setting-value {
    text-align: right;
}

.security-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.security-item:last-child {
    border-bottom: none;
}
</style>

<?php
$pageScript = "
function clearCache(type) {
    AdminPanel.confirm('Are you sure you want to clear the ' + type + ' cache?', function() {
        AdminPanel.showToast('Cache cleared successfully!', 'success');
        // In a real implementation, this would make an AJAX call to clear cache
    });
}

function refreshData() {
    AdminPanel.showLoading();
    setTimeout(function() {
        AdminPanel.hideLoading();
        AdminPanel.showToast('Data refreshed successfully!', 'success');
        location.reload();
    }, 2000);
}

function exportData() {
    AdminPanel.showToast('Export feature coming soon!', 'info');
}

function viewLogs() {
    AdminPanel.showToast('Log viewer coming soon!', 'info');
}
";

require_once 'includes/footer.php';
?>
