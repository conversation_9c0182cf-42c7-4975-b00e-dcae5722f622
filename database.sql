-- Database schema for Telegram Referral Bot
-- Run this script to create all necessary tables

CREATE DATABASE IF NOT EXISTS referbot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE referbot_db;

-- Users table
CREATE TABLE users (
    user_id BIGINT PRIMARY KEY,
    first_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    banned BOOLEAN DEFAULT FALSE,
    referred BOOLEAN DEFAULT FALSE,
    referred_by VARCHAR(50) DEFAULT 'None',
    joining_bonus_got DECIMAL(10,2) DEFAULT 0,
    referral_link TEXT,
    balance DECIMAL(10,2) DEFAULT 0,
    successful_withdraw DECIMAL(10,2) DEFAULT 0,
    withdraw_under_review DECIMAL(10,2) DEFAULT 0,
    gift_claimed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_referred_by (referred_by),
    INDEX idx_banned (banned),
    INDEX idx_referred (referred)
);

-- User account information
CREATE TABLE user_accounts (
    user_id BIGINT PRIMARY KEY,
    name VARCHAR(255) DEFAULT '',
    ifsc VARCHAR(20) DEFAULT '',
    email VARCHAR(255) DEFAULT '',
    account_number VARCHAR(50) DEFAULT '',
    mobile_number VARCHAR(20) DEFAULT '',
    usdt_address VARCHAR(42) DEFAULT '',
    withdrawal_method ENUM('bank', 'usdt') DEFAULT 'bank',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Admin panel settings
CREATE TABLE admin_settings (
    admin_id BIGINT PRIMARY KEY,
    main_channel VARCHAR(255) DEFAULT '',
    private_logs_channel VARCHAR(255) DEFAULT '',
    maintenance_status ENUM('On', 'Off') DEFAULT 'Off',
    otp_website_api_key VARCHAR(255) DEFAULT '',
    per_refer_amount DECIMAL(10,2) DEFAULT 0,
    joining_bonus_amount DECIMAL(10,2) DEFAULT 0,
    per_refer_amount_range VARCHAR(20) DEFAULT '20-50',
    joining_bonus_amount_range VARCHAR(20) DEFAULT '20-50',
    gift_channel VARCHAR(255) DEFAULT '',
    gift_amount DECIMAL(10,2) DEFAULT 0,
    gift_broadcast_id VARCHAR(255) DEFAULT '',
    force_sub_channels JSON DEFAULT '[]',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Promotion reports (referral tracking)
CREATE TABLE promotion_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id BIGINT NOT NULL,
    referred_user_name VARCHAR(255) NOT NULL,
    referred_user_id BIGINT NOT NULL,
    amount_got DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred_user (referred_user_id)
);

-- Withdrawal reports
CREATE TABLE withdrawal_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    date DATE NOT NULL,
    status ENUM('Under review', 'Passed', 'Failed') DEFAULT 'Under review',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_status (status)
);

-- User sessions for multi-step operations
CREATE TABLE user_sessions (
    user_id BIGINT PRIMARY KEY,
    step VARCHAR(100) NOT NULL,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_created (created_at)
);

-- Bot information cache
CREATE TABLE bot_info (
    id INT PRIMARY KEY DEFAULT 1,
    username VARCHAR(255),
    first_name VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin settings for all configured admins
INSERT INTO admin_settings (admin_id) VALUES (8153676253)
ON DUPLICATE KEY UPDATE admin_id = admin_id;

INSERT INTO admin_settings (admin_id) VALUES (2027123358)
ON DUPLICATE KEY UPDATE admin_id = admin_id;

-- Insert gift broadcast storage (admin_id = 0 for system-wide gift broadcasts)
INSERT INTO admin_settings (admin_id) VALUES (0)
ON DUPLICATE KEY UPDATE admin_id = admin_id;

-- Insert default bot info (will be updated by the bot)
INSERT INTO bot_info (id, username, first_name) VALUES (1, '', '')
ON DUPLICATE KEY UPDATE id = id;

-- Create indexes for better performance
CREATE INDEX idx_users_balance ON users(balance);
CREATE INDEX idx_users_created ON users(created_at);
CREATE INDEX idx_withdrawals_date ON withdrawal_reports(date);
CREATE INDEX idx_promotions_created ON promotion_reports(created_at);

-- Views for easier data access
CREATE VIEW user_stats AS
SELECT
    u.user_id,
    u.first_name,
    u.username,
    u.balance,
    u.successful_withdraw,
    u.withdraw_under_review,
    u.joining_bonus_got,
    u.referred,
    u.referred_by,
    u.banned,
    u.created_at,
    ua.name as account_name,
    ua.mobile_number,
    ua.email,
    (SELECT COUNT(*) FROM promotion_reports WHERE referrer_id = u.user_id) as total_referrals,
    (SELECT SUM(amount_got) FROM promotion_reports WHERE referrer_id = u.user_id) as total_referral_earnings
FROM users u
LEFT JOIN user_accounts ua ON u.user_id = ua.user_id;

-- View for admin dashboard
CREATE VIEW admin_dashboard AS
SELECT
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM users WHERE banned = TRUE) as banned_users,
    (SELECT COUNT(*) FROM users WHERE created_at >= CURDATE()) as new_users_today,
    (SELECT COUNT(*) FROM withdrawal_reports WHERE status = 'Under review') as pending_withdrawals,
    (SELECT SUM(amount) FROM withdrawal_reports WHERE status = 'Under review') as pending_withdrawal_amount,
    (SELECT SUM(balance) FROM users) as total_user_balance,
    (SELECT SUM(successful_withdraw) FROM users) as total_successful_withdrawals;

-- Tasks table for Extra Rewards system
CREATE TABLE tasks (
    task_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    reward_amount DECIMAL(10,2) NOT NULL,
    media_url TEXT DEFAULT '',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_by (created_by)
);

-- Gift codes table for Extra Rewards system
CREATE TABLE gift_codes (
    code VARCHAR(50) PRIMARY KEY,
    amount DECIMAL(10,2) NOT NULL,
    usage_limit INT DEFAULT 0,
    used_count INT DEFAULT 0,
    expiry_date TIMESTAMP NULL,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL,
    INDEX idx_created_by (created_by),
    INDEX idx_expiry (expiry_date)
);

-- Task submissions table for Extra Rewards system
CREATE TABLE task_submissions (
    submission_id VARCHAR(50) PRIMARY KEY,
    user_id BIGINT NOT NULL,
    task_id VARCHAR(50) NOT NULL,
    file_id VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    admin_note TEXT DEFAULT '',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_submitted (submitted_at)
);
