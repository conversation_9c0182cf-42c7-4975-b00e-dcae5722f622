<?php
// Emergency dashboard for shared hosting environments
// This works with minimal dependencies and no restricted functions

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple authentication check
if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    header('Location: simple_login.php');
    exit;
}

// Logout functionality
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple_login.php');
    exit;
}

// Load data safely
$stats = [
    'total_users' => 0,
    'total_balance' => 0,
    'total_withdrawals' => 0,
    'total_referrals' => 0,
    'banned_users' => 0,
    'pending_withdrawals' => 0
];

$data_source = 'none';
$error_message = '';

// Try to load data from JSON files
try {
    $data_dir = __DIR__ . '/../data/';
    $users_file = $data_dir . 'users.json';
    
    if (file_exists($users_file) && is_readable($users_file)) {
        $users_content = file_get_contents($users_file);
        
        if ($users_content !== false) {
            $users = json_decode($users_content, true);
            
            if (is_array($users) && !empty($users)) {
                $data_source = 'json';
                $stats['total_users'] = count($users);
                
                foreach ($users as $user) {
                    $stats['total_balance'] += floatval($user['balance'] ?? 0);
                    $stats['total_withdrawals'] += floatval($user['successful_withdraw'] ?? 0);
                    $stats['pending_withdrawals'] += floatval($user['withdraw_under_review'] ?? 0);
                    
                    if ($user['banned'] ?? false) {
                        $stats['banned_users']++;
                    }
                    
                    if (isset($user['promotion_report']) && is_array($user['promotion_report'])) {
                        $stats['total_referrals'] += count($user['promotion_report']);
                    }
                }
            }
        }
    }
} catch (Exception $e) {
    $error_message = "Error loading data: " . $e->getMessage();
}

// Format currency function
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

// Format number function
function formatNumber($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'K';
    }
    return number_format($number);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Admin Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f6fa; }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 { font-size: 1.5rem; }
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .logout-btn:hover { background: rgba(255,255,255,0.3); }
        
        .container { max-width: 1200px; margin: 2rem auto; padding: 0 1rem; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        .stat-card:hover { transform: translateY(-2px); }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .info-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .info-section h3 {
            color: #333;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .status-item:last-child { border-bottom: none; }
        
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        
        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .nav-link {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            transition: background 0.3s;
            font-weight: 500;
        }
        .nav-link:hover { background: #5a6fd8; }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Emergency Admin Dashboard</h1>
        <a href="?logout=1" class="logout-btn">🚪 Logout</a>
    </div>
    
    <div class="container">
        <?php if ($data_source === 'none'): ?>
            <div class="alert alert-warning">
                ⚠️ <strong>No data loaded.</strong> This could be normal for new installations or indicate a configuration issue.
                <?php if ($error_message): ?>
                    <br><strong>Error:</strong> <?php echo htmlspecialchars($error_message); ?>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                ✅ <strong>Data loaded successfully</strong> from <?php echo strtoupper($data_source); ?> storage.
            </div>
        <?php endif; ?>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">👥</div>
                <div class="stat-value"><?php echo formatNumber($stats['total_users']); ?></div>
                <div class="stat-label">Total Users</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">💰</div>
                <div class="stat-value"><?php echo formatCurrency($stats['total_balance']); ?></div>
                <div class="stat-label">Total Balance</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">💸</div>
                <div class="stat-value"><?php echo formatCurrency($stats['total_withdrawals']); ?></div>
                <div class="stat-label">Total Withdrawals</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">🔗</div>
                <div class="stat-value"><?php echo formatNumber($stats['total_referrals']); ?></div>
                <div class="stat-label">Total Referrals</div>
            </div>
        </div>
        
        <div class="info-section">
            <h3>🔧 System Status</h3>
            <div class="status-item">
                <span>PHP Version</span>
                <span class="status-good"><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="status-item">
                <span>Server Software</span>
                <span class="status-good"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
            </div>
            <div class="status-item">
                <span>Data Source</span>
                <span class="<?php echo $data_source === 'json' ? 'status-good' : 'status-warning'; ?>">
                    <?php echo strtoupper($data_source); ?>
                </span>
            </div>
            <div class="status-item">
                <span>Session Status</span>
                <span class="status-good">✅ Active</span>
            </div>
            <div class="status-item">
                <span>Memory Usage</span>
                <span class="status-good"><?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</span>
            </div>
        </div>
        
        <div class="info-section">
            <h3>🚀 Available Tools</h3>
            <div class="nav-links">
                <a href="hosting_check.php" class="nav-link">🔍 Hosting Check</a>
                <a href="debug.php" class="nav-link">🐛 Debug Info</a>
                <a href="test.php" class="nav-link">🧪 System Test</a>
                <a href="view_errors.php" class="nav-link">📋 Error Logs</a>
                <a href="fix_permissions.php" class="nav-link">🔧 Fix Permissions</a>
                <a href="login.php" class="nav-link">🎨 Full Admin Panel</a>
            </div>
        </div>
        
        <div class="info-section">
            <h3>ℹ️ About Emergency Dashboard</h3>
            <p>This emergency dashboard is designed to work on any shared hosting environment, including Hostinger. It uses minimal dependencies and avoids restricted functions like <code>exec()</code>.</p>
            
            <p style="margin-top: 1rem;"><strong>Features:</strong></p>
            <ul style="margin-left: 2rem; margin-top: 0.5rem;">
                <li>✅ No restricted function dependencies</li>
                <li>✅ PHP 8.2 compatible</li>
                <li>✅ Shared hosting optimized</li>
                <li>✅ Graceful error handling</li>
                <li>✅ Basic statistics display</li>
            </ul>
        </div>
    </div>
</body>
</html>
