<?php
// Data Access Layer for Admin Panel
// Handles both JSON and MySQL storage modes with performance optimization

require_once 'config.php';

class AdminDataAccess {
    private $storageMode;
    private $pdo;
    
    public function __construct() {
        $this->storageMode = defined('STORAGE_MODE') ? STORAGE_MODE : 'json';

        if ($this->storageMode === 'mysql') {
            try {
                $this->pdo = getAdminDB();
                if ($this->pdo === null) {
                    // Fallback to JSON mode if database connection fails
                    $this->storageMode = 'json';
                    error_log("Database connection failed, falling back to JSON mode");
                }
            } catch (Exception $e) {
                $this->storageMode = 'json';
                error_log("Database error, falling back to JSON mode: " . $e->getMessage());
            }
        }
    }
    
    // Dashboard Statistics
    public function getDashboardStats() {
        $cacheKey = 'dashboard_stats';
        $cached = getCachedData($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        if ($this->storageMode === 'mysql') {
            $stats = $this->getMySQLDashboardStats();
        } else {
            $stats = $this->getJSONDashboardStats();
        }
        
        setCachedData($cacheKey, $stats, 300); // Cache for 5 minutes
        return $stats;
    }
    
    private function getMySQLDashboardStats() {
        $stats = [];
        
        // Basic counts
        $stmt = $this->pdo->query("
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN banned = 1 THEN 1 END) as banned_users,
                COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as new_users_today,
                COUNT(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d,
                COUNT(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as active_users_30d,
                SUM(balance) as total_user_balance,
                SUM(successful_withdraw) as total_withdrawals,
                SUM(withdraw_under_review) as pending_withdrawals
            FROM users
        ");
        $stats = $stmt->fetch();
        
        // Referral stats
        $stmt = $this->pdo->query("SELECT COUNT(*) as total_referrals FROM promotion_reports");
        $referralStats = $stmt->fetch();
        $stats['total_referrals'] = $referralStats['total_referrals'];
        
        // Withdrawal requests
        $stmt = $this->pdo->query("
            SELECT 
                COUNT(*) as pending_withdrawal_requests,
                SUM(amount) as pending_withdrawal_amount
            FROM withdrawal_reports 
            WHERE status = 'Under review'
        ");
        $withdrawalStats = $stmt->fetch();
        $stats['pending_withdrawal_requests'] = $withdrawalStats['pending_withdrawal_requests'] ?? 0;
        $stats['pending_withdrawal_amount'] = $withdrawalStats['pending_withdrawal_amount'] ?? 0;
        
        return $stats;
    }
    
    private function getJSONDashboardStats() {
        $users = $this->loadJSONFile(USERS_FILE);
        $stats = [
            'total_users' => count($users),
            'banned_users' => 0,
            'new_users_today' => 0,
            'active_users_7d' => 0,
            'active_users_30d' => 0,
            'total_user_balance' => 0,
            'total_withdrawals' => 0,
            'pending_withdrawals' => 0,
            'total_referrals' => 0,
            'pending_withdrawal_requests' => 0,
            'pending_withdrawal_amount' => 0
        ];
        
        $today = date('Y-m-d');
        $sevenDaysAgo = date('Y-m-d', strtotime('-7 days'));
        $thirtyDaysAgo = date('Y-m-d', strtotime('-30 days'));
        
        foreach ($users as $user) {
            if ($user['banned']) $stats['banned_users']++;
            
            $stats['total_user_balance'] += $user['balance'] ?? 0;
            $stats['total_withdrawals'] += $user['successful_withdraw'] ?? 0;
            $stats['pending_withdrawals'] += $user['withdraw_under_review'] ?? 0;
            
            // Count referrals
            if (isset($user['promotion_report'])) {
                $stats['total_referrals'] += count($user['promotion_report']);
            }
            
            // For JSON mode, we'll estimate activity based on user creation
            // In a real implementation, you'd track last_activity
            $stats['active_users_7d']++;
            $stats['active_users_30d']++;
            $stats['new_users_today']++;
        }
        
        return $stats;
    }
    
    // User Management
    public function getUsers($page = 1, $pageSize = DEFAULT_PAGE_SIZE, $search = '', $filter = 'all') {
        if ($this->storageMode === 'mysql') {
            return $this->getMySQLUsers($page, $pageSize, $search, $filter);
        } else {
            return $this->getJSONUsers($page, $pageSize, $search, $filter);
        }
    }
    
    private function getMySQLUsers($page, $pageSize, $search, $filter) {
        $offset = ($page - 1) * $pageSize;
        $whereConditions = [];
        $params = [];
        
        // Search conditions
        if (!empty($search)) {
            $whereConditions[] = "(first_name LIKE ? OR username LIKE ? OR user_id LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        // Filter conditions
        switch ($filter) {
            case 'banned':
                $whereConditions[] = "banned = 1";
                break;
            case 'active':
                $whereConditions[] = "banned = 0";
                break;
            case 'high_balance':
                $whereConditions[] = "balance > 1000";
                break;
            case 'pending_withdrawal':
                $whereConditions[] = "withdraw_under_review > 0";
                break;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
        $stmt = $this->pdo->prepare($countQuery);
        $stmt->execute($params);
        $totalRecords = $stmt->fetch()['total'];
        
        // Get users with referral counts
        $query = "
            SELECT 
                u.*,
                ua.name as account_name,
                ua.mobile_number,
                ua.email,
                (SELECT COUNT(*) FROM promotion_reports WHERE referrer_id = u.user_id) as total_referrals
            FROM users u
            LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
            $whereClause
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $pageSize;
        $params[] = $offset;
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
        return [
            'users' => $users,
            'pagination' => getPaginationData($page, $totalRecords, $pageSize)
        ];
    }
    
    private function getJSONUsers($page, $pageSize, $search, $filter) {
        $users = $this->loadJSONFile(USERS_FILE);
        $filteredUsers = [];
        
        foreach ($users as $user) {
            // Apply search filter
            if (!empty($search)) {
                $searchLower = strtolower($search);
                if (strpos(strtolower($user['first_name'] ?? ''), $searchLower) === false &&
                    strpos(strtolower($user['username'] ?? ''), $searchLower) === false &&
                    strpos((string)$user['user_id'], $search) === false) {
                    continue;
                }
            }
            
            // Apply status filter
            switch ($filter) {
                case 'banned':
                    if (!($user['banned'] ?? false)) continue 2;
                    break;
                case 'active':
                    if ($user['banned'] ?? false) continue 2;
                    break;
                case 'high_balance':
                    if (($user['balance'] ?? 0) <= 1000) continue 2;
                    break;
                case 'pending_withdrawal':
                    if (($user['withdraw_under_review'] ?? 0) <= 0) continue 2;
                    break;
            }
            
            // Add referral count
            $user['total_referrals'] = count($user['promotion_report'] ?? []);
            $user['account_name'] = $user['account_info']['name'] ?? '';
            $user['mobile_number'] = $user['account_info']['mobile_number'] ?? '';
            $user['email'] = $user['account_info']['email'] ?? '';
            
            $filteredUsers[] = $user;
        }
        
        $totalRecords = count($filteredUsers);
        $offset = ($page - 1) * $pageSize;
        $paginatedUsers = array_slice($filteredUsers, $offset, $pageSize);
        
        return [
            'users' => $paginatedUsers,
            'pagination' => getPaginationData($page, $totalRecords, $pageSize)
        ];
    }
    
    // Financial data
    public function getFinancialStats() {
        $cacheKey = 'financial_stats';
        $cached = getCachedData($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        if ($this->storageMode === 'mysql') {
            $stats = $this->getMySQLFinancialStats();
        } else {
            $stats = $this->getJSONFinancialStats();
        }
        
        setCachedData($cacheKey, $stats, 600); // Cache for 10 minutes
        return $stats;
    }
    
    private function getMySQLFinancialStats() {
        // Implementation for MySQL financial stats
        $stmt = $this->pdo->query("
            SELECT 
                SUM(successful_withdraw) as total_withdrawals,
                SUM(withdraw_under_review) as pending_withdrawals,
                SUM(balance) as total_balances,
                AVG(balance) as avg_balance,
                COUNT(CASE WHEN balance > 0 THEN 1 END) as users_with_balance
            FROM users
        ");
        
        return $stmt->fetch();
    }
    
    private function getJSONFinancialStats() {
        $users = $this->loadJSONFile(USERS_FILE);
        $stats = [
            'total_withdrawals' => 0,
            'pending_withdrawals' => 0,
            'total_balances' => 0,
            'avg_balance' => 0,
            'users_with_balance' => 0
        ];
        
        $totalBalance = 0;
        $userCount = 0;
        
        foreach ($users as $user) {
            $stats['total_withdrawals'] += $user['successful_withdraw'] ?? 0;
            $stats['pending_withdrawals'] += $user['withdraw_under_review'] ?? 0;
            $balance = $user['balance'] ?? 0;
            $stats['total_balances'] += $balance;
            $totalBalance += $balance;
            $userCount++;
            
            if ($balance > 0) {
                $stats['users_with_balance']++;
            }
        }
        
        $stats['avg_balance'] = $userCount > 0 ? $totalBalance / $userCount : 0;
        
        return $stats;
    }
    
    // Utility function to load JSON files
    private function loadJSONFile($filename) {
        if (!file_exists($filename)) {
            return [];
        }
        
        $content = file_get_contents($filename);
        return json_decode($content, true) ?? [];
    }
    
    // Get leaderboard data
    public function getLeaderboards() {
        $cacheKey = 'leaderboards';
        $cached = getCachedData($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        if ($this->storageMode === 'mysql') {
            $data = $this->getMySQLLeaderboards();
        } else {
            $data = $this->getJSONLeaderboards();
        }
        
        setCachedData($cacheKey, $data, 900); // Cache for 15 minutes
        return $data;
    }
    
    private function getMySQLLeaderboards() {
        $leaderboards = [];
        
        // Top referrers
        $stmt = $this->pdo->query("
            SELECT u.user_id, u.first_name, u.username, COUNT(pr.id) as referral_count
            FROM users u
            LEFT JOIN promotion_reports pr ON u.user_id = pr.referrer_id
            GROUP BY u.user_id
            ORDER BY referral_count DESC
            LIMIT 10
        ");
        $leaderboards['top_referrers'] = $stmt->fetchAll();
        
        // Top withdrawers
        $stmt = $this->pdo->query("
            SELECT user_id, first_name, username, successful_withdraw
            FROM users
            WHERE successful_withdraw > 0
            ORDER BY successful_withdraw DESC
            LIMIT 10
        ");
        $leaderboards['top_withdrawers'] = $stmt->fetchAll();
        
        // Top balances
        $stmt = $this->pdo->query("
            SELECT user_id, first_name, username, balance
            FROM users
            WHERE balance > 0
            ORDER BY balance DESC
            LIMIT 10
        ");
        $leaderboards['top_balances'] = $stmt->fetchAll();
        
        return $leaderboards;
    }
    
    private function getJSONLeaderboards() {
        $users = $this->loadJSONFile(USERS_FILE);
        $leaderboards = [
            'top_referrers' => [],
            'top_withdrawers' => [],
            'top_balances' => []
        ];
        
        // Process users for leaderboards
        $referrers = [];
        $withdrawers = [];
        $balances = [];
        
        foreach ($users as $user) {
            $referralCount = count($user['promotion_report'] ?? []);
            $withdrawAmount = $user['successful_withdraw'] ?? 0;
            $balance = $user['balance'] ?? 0;
            
            if ($referralCount > 0) {
                $referrers[] = [
                    'user_id' => $user['user_id'],
                    'first_name' => $user['first_name'],
                    'username' => $user['username'] ?? '',
                    'referral_count' => $referralCount
                ];
            }
            
            if ($withdrawAmount > 0) {
                $withdrawers[] = [
                    'user_id' => $user['user_id'],
                    'first_name' => $user['first_name'],
                    'username' => $user['username'] ?? '',
                    'successful_withdraw' => $withdrawAmount
                ];
            }
            
            if ($balance > 0) {
                $balances[] = [
                    'user_id' => $user['user_id'],
                    'first_name' => $user['first_name'],
                    'username' => $user['username'] ?? '',
                    'balance' => $balance
                ];
            }
        }
        
        // Sort and limit
        usort($referrers, function($a, $b) { return $b['referral_count'] - $a['referral_count']; });
        usort($withdrawers, function($a, $b) { return $b['successful_withdraw'] - $a['successful_withdraw']; });
        usort($balances, function($a, $b) { return $b['balance'] - $a['balance']; });
        
        $leaderboards['top_referrers'] = array_slice($referrers, 0, 10);
        $leaderboards['top_withdrawers'] = array_slice($withdrawers, 0, 10);
        $leaderboards['top_balances'] = array_slice($balances, 0, 10);
        
        return $leaderboards;
    }

    // Get single user details
    public function getUserDetails($userId) {
        if ($this->storageMode === 'mysql') {
            return $this->getMySQLUserDetails($userId);
        } else {
            return $this->getJSONUserDetails($userId);
        }
    }

    private function getMySQLUserDetails($userId) {
        $stmt = $this->pdo->prepare("
            SELECT
                u.*,
                ua.name as account_name,
                ua.ifsc,
                ua.email,
                ua.account_number,
                ua.mobile_number,
                ua.usdt_address,
                ua.withdrawal_method
            FROM users u
            LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
            WHERE u.user_id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) return null;

        // Get referral history
        $stmt = $this->pdo->prepare("
            SELECT * FROM promotion_reports
            WHERE referrer_id = ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$userId]);
        $user['promotion_report'] = $stmt->fetchAll();

        return $user;
    }

    private function getJSONUserDetails($userId) {
        $users = $this->loadJSONFile(USERS_FILE);
        return $users[$userId] ?? null;
    }
}
?>
