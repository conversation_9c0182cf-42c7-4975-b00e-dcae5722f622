<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load users
$users = [];
$error = '';

try {
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
            if (!is_array($users)) {
                $users = [];
                $error = 'Invalid JSON format';
            }
        }
    } else {
        $error = 'Users file not found';
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Handle user actions
$action_message = '';
if ($_POST && isset($_POST['action']) && isset($_POST['user_id'])) {
    $user_id = $_POST['user_id'];
    $action = $_POST['action'];
    
    if (isset($users[$user_id])) {
        if ($action === 'ban') {
            $users[$user_id]['banned'] = true;
            $action_message = "User $user_id has been banned.";
        } elseif ($action === 'unban') {
            $users[$user_id]['banned'] = false;
            $action_message = "User $user_id has been unbanned.";
        }
        
        // Save changes
        try {
            file_put_contents($users_file, json_encode($users, JSON_PRETTY_PRINT));
        } catch (Exception $e) {
            $action_message = "Error saving changes: " . $e->getMessage();
        }
    }
}

// Search and filter
$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? 'all';
$sort = $_GET['sort'] ?? 'id';

$filtered_users = [];
foreach ($users as $user_id => $user) {
    // Apply search
    if (!empty($search)) {
        $search_lower = strtolower($search);
        if (strpos(strtolower($user['first_name'] ?? ''), $search_lower) === false &&
            strpos(strtolower($user['username'] ?? ''), $search_lower) === false &&
            strpos($user_id, $search) === false) {
            continue;
        }
    }
    
    // Apply filter
    switch ($filter) {
        case 'banned':
            if (!($user['banned'] ?? false)) continue 2;
            break;
        case 'active':
            if ($user['banned'] ?? false) continue 2;
            break;
        case 'high_balance':
            if (($user['balance'] ?? 0) < 1000) continue 2;
            break;
        case 'referred':
            if (!($user['referred'] ?? false)) continue 2;
            break;
    }
    
    $user['user_id'] = $user_id;
    $user['referral_count'] = count($user['promotion_report'] ?? []);
    $filtered_users[] = $user;
}

// Sort users
usort($filtered_users, function($a, $b) use ($sort) {
    switch ($sort) {
        case 'name':
            return strcasecmp($a['first_name'] ?? '', $b['first_name'] ?? '');
        case 'balance':
            return ($b['balance'] ?? 0) - ($a['balance'] ?? 0);
        case 'referrals':
            return $b['referral_count'] - $a['referral_count'];
        case 'withdrawals':
            return ($b['successful_withdraw'] ?? 0) - ($a['successful_withdraw'] ?? 0);
        default:
            return strcmp($a['user_id'], $b['user_id']);
    }
});

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 25;
$total_users = count($filtered_users);
$total_pages = ceil($total_users / $per_page);
$offset = ($page - 1) * $per_page;
$users_page = array_slice($filtered_users, $offset, $per_page);
?>
<!DOCTYPE html>
<html>
<head>
    <title>User Management</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .controls { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .controls-row { display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 15px; align-items: end; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { padding: 10px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; text-align: center; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .stats-bar { background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
        .users-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; position: sticky; top: 0; }
        tr:hover { background: #f8f9fa; }
        .pagination { text-align: center; margin: 20px 0; }
        .pagination a { display: inline-block; padding: 8px 12px; margin: 0 2px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .pagination a:hover { background: #0056b3; }
        .pagination .current { background: #28a745; }
        .user-actions { display: flex; gap: 5px; }
        .user-actions button { padding: 5px 10px; font-size: 12px; border: none; border-radius: 3px; cursor: pointer; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; }
        .status-active { background: #d4edda; color: #155724; }
        .status-banned { background: #f8d7da; color: #721c24; }
        .status-referred { background: #cce5ff; color: #004085; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.success { background: #d4edda; color: #155724; }
        .message.error { background: #f8d7da; color: #721c24; }
        @media (max-width: 768px) {
            .controls-row { grid-template-columns: 1fr; }
            .stats-bar { flex-direction: column; gap: 10px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 User Management</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($action_message): ?>
            <div class="message success">✅ <?php echo htmlspecialchars($action_message); ?></div>
        <?php endif; ?>
        
        <!-- Search and Filter Controls -->
        <div class="controls">
            <form method="GET" action="">
                <div class="controls-row">
                    <div class="form-group">
                        <label>Search Users</label>
                        <input type="text" name="search" placeholder="Name, username, or user ID" value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="form-group">
                        <label>Filter</label>
                        <select name="filter">
                            <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Users</option>
                            <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Only</option>
                            <option value="banned" <?php echo $filter === 'banned' ? 'selected' : ''; ?>>Banned Only</option>
                            <option value="high_balance" <?php echo $filter === 'high_balance' ? 'selected' : ''; ?>>High Balance (₹1000+)</option>
                            <option value="referred" <?php echo $filter === 'referred' ? 'selected' : ''; ?>>Referred Users</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Sort By</label>
                        <select name="sort">
                            <option value="id" <?php echo $sort === 'id' ? 'selected' : ''; ?>>User ID</option>
                            <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                            <option value="balance" <?php echo $sort === 'balance' ? 'selected' : ''; ?>>Balance</option>
                            <option value="referrals" <?php echo $sort === 'referrals' ? 'selected' : ''; ?>>Referrals</option>
                            <option value="withdrawals" <?php echo $sort === 'withdrawals' ? 'selected' : ''; ?>>Withdrawals</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn">🔍 Search</button>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <a href="user_management.php" class="btn btn-warning">🔄 Reset</a>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Statistics Bar -->
        <div class="stats-bar">
            <div>
                <strong>Total Users:</strong> <?php echo number_format(count($users)); ?> | 
                <strong>Filtered:</strong> <?php echo number_format($total_users); ?> | 
                <strong>Showing:</strong> <?php echo count($users_page); ?>
            </div>
            <div>
                <strong>Page:</strong> <?php echo $page; ?> of <?php echo $total_pages; ?>
            </div>
        </div>
        
        <!-- Users Table -->
        <?php if (!empty($users_page)): ?>
            <div class="users-table">
                <table>
                    <thead>
                        <tr>
                            <th>User ID</th>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Balance</th>
                            <th>Withdrawals</th>
                            <th>Referrals</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users_page as $user): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($user['user_id']); ?></strong>
                                    <?php if ($user['referred'] ?? false): ?>
                                        <span class="status-badge status-referred">Referred</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($user['first_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($user['username'] ?? 'N/A'); ?></td>
                                <td>
                                    <strong>₹<?php echo number_format($user['balance'] ?? 0, 2); ?></strong>
                                    <?php if (($user['withdraw_under_review'] ?? 0) > 0): ?>
                                        <br><small style="color: orange;">₹<?php echo number_format($user['withdraw_under_review'], 2); ?> pending</small>
                                    <?php endif; ?>
                                </td>
                                <td>₹<?php echo number_format($user['successful_withdraw'] ?? 0, 2); ?></td>
                                <td>
                                    <strong><?php echo $user['referral_count']; ?></strong>
                                    <?php if ($user['referral_count'] > 0): ?>
                                        <br><a href="referral_chains.php?user_id=<?php echo $user['user_id']; ?>" style="font-size: 12px;">View Chain</a>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['banned'] ?? false): ?>
                                        <span class="status-badge status-banned">❌ Banned</span>
                                    <?php else: ?>
                                        <span class="status-badge status-active">✅ Active</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="user-actions">
                                        <a href="user_profile.php?id=<?php echo $user['user_id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">👁️ View</a>
                                        
                                        <?php if ($user['banned'] ?? false): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                <input type="hidden" name="action" value="unban">
                                                <button type="submit" class="btn-success" onclick="return confirm('Unban this user?')">✅ Unban</button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                <input type="hidden" name="action" value="ban">
                                                <button type="submit" class="btn-danger" onclick="return confirm('Ban this user?')">❌ Ban</button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&sort=<?php echo $sort; ?>">← Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&sort=<?php echo $sort; ?>" 
                           <?php echo $i === $page ? 'class="current"' : ''; ?>>
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>&sort=<?php echo $sort; ?>">Next →</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="message">No users found matching your criteria.</div>
        <?php endif; ?>
    </div>
</body>
</html>
