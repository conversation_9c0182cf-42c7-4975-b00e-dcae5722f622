<?php
$pageTitle = 'User Management';
require_once 'includes/header.php';
require_once 'data_access.php';

$dataAccess = new AdminDataAccess();

// Get parameters
$page = max(1, intval($_GET['page'] ?? 1));
$pageSize = min(MAX_PAGE_SIZE, max(10, intval($_GET['pageSize'] ?? DEFAULT_PAGE_SIZE)));
$search = trim($_GET['search'] ?? '');
$filter = $_GET['filter'] ?? 'all';

// Get users data
$userData = $dataAccess->getUsers($page, $pageSize, $search, $filter);
$users = $userData['users'];
$pagination = $userData['pagination'];

// Filter options
$filterOptions = [
    'all' => 'All Users',
    'active' => 'Active Users',
    'banned' => 'Banned Users',
    'high_balance' => 'High Balance (>₹1000)',
    'pending_withdrawal' => 'Pending Withdrawal',
    'referred' => 'Referred Users',
    'direct' => 'Direct Users'
];
?>

<!-- Search and Filter Section -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="stats-card">
            <form method="GET" action="" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Users</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               id="search" 
                               name="search" 
                               placeholder="Name, username, or user ID"
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label for="filter" class="form-label">Filter by Status</label>
                    <select class="form-select" id="filter" name="filter">
                        <?php foreach ($filterOptions as $value => $label): ?>
                            <option value="<?php echo $value; ?>" <?php echo $filter === $value ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="pageSize" class="form-label">Per Page</label>
                    <select class="form-select" id="pageSize" name="pageSize">
                        <option value="25" <?php echo $pageSize === 25 ? 'selected' : ''; ?>>25</option>
                        <option value="50" <?php echo $pageSize === 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $pageSize === 100 ? 'selected' : ''; ?>>100</option>
                        <option value="200" <?php echo $pageSize === 200 ? 'selected' : ''; ?>>200</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Search
                    </button>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Results Summary -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-1">
                    <i class="fas fa-users me-2 text-primary"></i>
                    User Management
                </h5>
                <p class="text-muted mb-0">
                    Showing <?php echo number_format(count($users)); ?> of <?php echo number_format($pagination['totalRecords']); ?> users
                    <?php if ($search): ?>
                        for "<?php echo htmlspecialchars($search); ?>"
                    <?php endif; ?>
                    <?php if ($filter !== 'all'): ?>
                        (<?php echo $filterOptions[$filter]; ?>)
                    <?php endif; ?>
                </p>
            </div>
            <div>
                <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="table-container">
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>User Info</th>
                    <th>Balance</th>
                    <th>Referrals</th>
                    <th>Withdrawals</th>
                    <th>Status</th>
                    <th>Registration</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No users found matching your criteria.</p>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                        <tr class="table-row-clickable" data-href="user_details.php?id=<?php echo $user['user_id']; ?>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($user['first_name']); ?></div>
                                        <div class="text-muted small">
                                            <?php if (!empty($user['username'])): ?>
                                                @<?php echo htmlspecialchars($user['username']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-muted small">ID: <?php echo $user['user_id']; ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="fw-bold text-success">
                                    <?php echo formatCurrency($user['balance'] ?? 0); ?>
                                </div>
                                <?php if (($user['withdraw_under_review'] ?? 0) > 0): ?>
                                    <div class="text-warning small">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo formatCurrency($user['withdraw_under_review']); ?> pending
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-bold text-primary">
                                    <?php echo number_format($user['total_referrals'] ?? 0); ?>
                                </div>
                                <?php if (($user['referred'] ?? false)): ?>
                                    <div class="text-muted small">
                                        <i class="fas fa-user-friends me-1"></i>
                                        Referred user
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-bold text-info">
                                    <?php echo formatCurrency($user['successful_withdraw'] ?? 0); ?>
                                </div>
                                <div class="text-muted small">Total withdrawn</div>
                            </td>
                            <td>
                                <?php if ($user['banned'] ?? false): ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-ban me-1"></i>
                                        Banned
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Active
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (($user['balance'] ?? 0) > 1000): ?>
                                    <span class="badge bg-warning text-dark ms-1">
                                        <i class="fas fa-star me-1"></i>
                                        High Balance
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="text-muted small">
                                    <?php 
                                    if (isset($user['created_at'])) {
                                        echo timeAgo($user['created_at']);
                                    } else {
                                        echo 'Unknown';
                                    }
                                    ?>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="user_details.php?id=<?php echo $user['user_id']; ?>" 
                                       class="btn btn-outline-primary"
                                       data-bs-toggle="tooltip"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-secondary"
                                            onclick="copyToClipboard('<?php echo $user['user_id']; ?>')"
                                            data-bs-toggle="tooltip"
                                            title="Copy User ID">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php if ($pagination['totalPages'] > 1): ?>
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted">
                Showing <?php echo number_format(($pagination['page'] - 1) * $pagination['pageSize'] + 1); ?> 
                to <?php echo number_format(min($pagination['page'] * $pagination['pageSize'], $pagination['totalRecords'])); ?> 
                of <?php echo number_format($pagination['totalRecords']); ?> entries
            </div>
            
            <nav aria-label="User pagination">
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($pagination['hasPrev']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['page'] - 1])); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php
                    $startPage = max(1, $pagination['page'] - 2);
                    $endPage = min($pagination['totalPages'], $pagination['page'] + 2);
                    
                    if ($startPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                        </li>
                        <?php if ($startPage > 2): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <li class="page-item <?php echo $i === $pagination['page'] ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($endPage < $pagination['totalPages']): ?>
                        <?php if ($endPage < $pagination['totalPages'] - 1): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['totalPages']])); ?>">
                                <?php echo $pagination['totalPages']; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if ($pagination['hasNext']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['page'] + 1])); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.table-row-clickable:hover {
    background-color: rgba(102, 126, 234, 0.05);
    cursor: pointer;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}
</style>

<?php
$pageScript = "
// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        AdminPanel.showToast('User ID copied to clipboard!', 'success');
    }).catch(function() {
        AdminPanel.showToast('Failed to copy to clipboard', 'error');
    });
}

// Auto-submit form on filter change
document.getElementById('filter').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('pageSize').addEventListener('change', function() {
    this.form.submit();
});

// Keyboard shortcut for search
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        document.getElementById('search').focus();
    }
});
";

require_once 'includes/footer.php';
?>
