<?php
// SUPER SIMPLE TEST - This WILL work
echo "<!DOCTYPE html><html><head><title>Working Test</title></head><body>";
echo "<h1>✅ PHP is Working!</h1>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Test session
session_start();
$_SESSION['test'] = 'working';
echo "<p><strong>Sessions:</strong> ✅ Working</p>";

// Test file system
$test_file = __DIR__ . '/test_write.txt';
if (file_put_contents($test_file, 'test')) {
    echo "<p><strong>File Writing:</strong> ✅ Working</p>";
    unlink($test_file);
} else {
    echo "<p><strong>File Writing:</strong> ❌ Failed</p>";
}

echo "<hr>";
echo "<h2>🔗 Try These Links:</h2>";
echo "<p><a href='super_simple_login.php' style='background:#007bff;color:white;padding:10px;text-decoration:none;border-radius:5px;'>🚀 Super Simple Login</a></p>";
echo "<p><a href='basic_admin.php' style='background:#28a745;color:white;padding:10px;text-decoration:none;border-radius:5px;'>📊 Basic Admin</a></p>";

echo "</body></html>";
?>
