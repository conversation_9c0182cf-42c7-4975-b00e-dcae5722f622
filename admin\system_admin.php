<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

$message = '';
$error = '';

// Handle actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'clear_cache':
            try {
                $cache_dir = __DIR__ . '/cache';
                if (is_dir($cache_dir)) {
                    $files = glob($cache_dir . '/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                    $message = 'Cache cleared successfully!';
                } else {
                    $message = 'Cache directory not found.';
                }
            } catch (Exception $e) {
                $error = 'Error clearing cache: ' . $e->getMessage();
            }
            break;
            
        case 'backup_data':
            try {
                $backup_dir = __DIR__ . '/backups';
                if (!is_dir($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                
                $timestamp = date('Y-m-d_H-i-s');
                $backup_file = $backup_dir . '/backup_' . $timestamp . '.zip';
                
                // Simple backup - copy important files
                $data_dir = __DIR__ . '/../data';
                if (is_dir($data_dir)) {
                    $zip = new ZipArchive();
                    if ($zip->open($backup_file, ZipArchive::CREATE) === TRUE) {
                        $files = glob($data_dir . '/*.json');
                        foreach ($files as $file) {
                            $zip->addFile($file, basename($file));
                        }
                        $zip->close();
                        $message = 'Backup created successfully: ' . basename($backup_file);
                    } else {
                        $error = 'Could not create backup file.';
                    }
                } else {
                    $error = 'Data directory not found.';
                }
            } catch (Exception $e) {
                $error = 'Error creating backup: ' . $e->getMessage();
            }
            break;
            
        case 'update_settings':
            try {
                $settings = [
                    'maintenance_mode' => isset($_POST['maintenance_mode']),
                    'registration_enabled' => isset($_POST['registration_enabled']),
                    'withdrawals_enabled' => isset($_POST['withdrawals_enabled']),
                    'referral_bonus' => floatval($_POST['referral_bonus'] ?? 0),
                    'joining_bonus' => floatval($_POST['joining_bonus'] ?? 0),
                    'min_withdrawal' => floatval($_POST['min_withdrawal'] ?? 0),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $settings_file = __DIR__ . '/../data/admin_settings.json';
                file_put_contents($settings_file, json_encode($settings, JSON_PRETTY_PRINT));
                $message = 'Settings updated successfully!';
            } catch (Exception $e) {
                $error = 'Error updating settings: ' . $e->getMessage();
            }
            break;
    }
}

// Load current settings
$current_settings = [
    'maintenance_mode' => false,
    'registration_enabled' => true,
    'withdrawals_enabled' => true,
    'referral_bonus' => 50,
    'joining_bonus' => 25,
    'min_withdrawal' => 100
];

try {
    $settings_file = __DIR__ . '/../data/admin_settings.json';
    if (file_exists($settings_file)) {
        $loaded_settings = json_decode(file_get_contents($settings_file), true);
        if (is_array($loaded_settings)) {
            $current_settings = array_merge($current_settings, $loaded_settings);
        }
    }
} catch (Exception $e) {
    // Use default settings
}

// System information
$system_info = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
    'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'
];

// File system info
$data_dir = __DIR__ . '/../data';
$cache_dir = __DIR__ . '/cache';
$backup_dir = __DIR__ . '/backups';

$file_info = [
    'data_dir_exists' => is_dir($data_dir),
    'data_dir_writable' => is_dir($data_dir) && is_writable($data_dir),
    'cache_dir_exists' => is_dir($cache_dir),
    'cache_dir_writable' => is_dir($cache_dir) && is_writable($cache_dir),
    'backup_dir_exists' => is_dir($backup_dir),
    'users_file_size' => file_exists($data_dir . '/users.json') ? filesize($data_dir . '/users.json') : 0,
    'withdrawals_file_size' => file_exists($data_dir . '/withdrawal_reports.json') ? filesize($data_dir . '/withdrawal_reports.json') : 0
];

// Get backup files
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = glob($backup_dir . '/backup_*.zip');
    foreach ($files as $file) {
        $backup_files[] = [
            'name' => basename($file),
            'size' => filesize($file),
            'date' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    // Sort by date (newest first)
    usort($backup_files, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
}

// Cache files info
$cache_files = [];
if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            $cache_files[] = [
                'name' => basename($file),
                'size' => filesize($file),
                'date' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>System Administration</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #6c757d, #495057); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .settings-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; box-sizing: border-box; }
        .form-group input[type="checkbox"] { width: auto; margin-right: 10px; }
        .checkbox-group { display: flex; align-items: center; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; text-align: center; margin-right: 10px; margin-bottom: 10px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .info-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .info-card h4 { margin: 0 0 10px 0; color: #333; }
        .info-item { display: flex; justify-content: space-between; margin: 5px 0; }
        .info-label { color: #666; }
        .info-value { font-weight: bold; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .file-list { max-height: 300px; overflow-y: auto; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .file-item:hover { background: #f8f9fa; }
        .file-info { flex: 1; }
        .file-size { color: #666; font-size: 0.9em; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.success { background: #d4edda; color: #155724; }
        .message.error { background: #f8d7da; color: #721c24; }
        .message.warning { background: #fff3cd; color: #856404; }
        .actions-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .action-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .action-card h4 { margin: 0 0 15px 0; color: #333; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ System Administration</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($message): ?>
            <div class="message success">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- System Settings -->
        <div class="content-section">
            <h3>🔧 System Settings</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_settings">
                
                <div class="settings-grid">
                    <div>
                        <h4>System Controls</h4>
                        
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" name="maintenance_mode" id="maintenance_mode" 
                                       <?php echo $current_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                <label for="maintenance_mode">Maintenance Mode</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" name="registration_enabled" id="registration_enabled" 
                                       <?php echo $current_settings['registration_enabled'] ? 'checked' : ''; ?>>
                                <label for="registration_enabled">Allow New Registrations</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" name="withdrawals_enabled" id="withdrawals_enabled" 
                                       <?php echo $current_settings['withdrawals_enabled'] ? 'checked' : ''; ?>>
                                <label for="withdrawals_enabled">Allow Withdrawals</label>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4>Financial Settings</h4>
                        
                        <div class="form-group">
                            <label for="referral_bonus">Referral Bonus (₹)</label>
                            <input type="number" name="referral_bonus" id="referral_bonus" 
                                   value="<?php echo $current_settings['referral_bonus']; ?>" step="0.01" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="joining_bonus">Joining Bonus (₹)</label>
                            <input type="number" name="joining_bonus" id="joining_bonus" 
                                   value="<?php echo $current_settings['joining_bonus']; ?>" step="0.01" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="min_withdrawal">Minimum Withdrawal (₹)</label>
                            <input type="number" name="min_withdrawal" id="min_withdrawal" 
                                   value="<?php echo $current_settings['min_withdrawal']; ?>" step="0.01" min="0">
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-success">💾 Save Settings</button>
            </form>
        </div>
        
        <!-- System Information -->
        <div class="content-section">
            <h3>📊 System Information</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>PHP Environment</h4>
                    <div class="info-item">
                        <span class="info-label">PHP Version:</span>
                        <span class="info-value"><?php echo $system_info['php_version']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Memory Limit:</span>
                        <span class="info-value"><?php echo $system_info['memory_limit']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Memory Usage:</span>
                        <span class="info-value"><?php echo $system_info['memory_usage']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Peak Memory:</span>
                        <span class="info-value"><?php echo $system_info['peak_memory']; ?></span>
                    </div>
                </div>
                
                <div class="info-card">
                    <h4>Server Environment</h4>
                    <div class="info-item">
                        <span class="info-label">Server:</span>
                        <span class="info-value"><?php echo $system_info['server_software']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Max Execution:</span>
                        <span class="info-value"><?php echo $system_info['max_execution_time']; ?>s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Upload Max:</span>
                        <span class="info-value"><?php echo $system_info['upload_max_filesize']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Post Max:</span>
                        <span class="info-value"><?php echo $system_info['post_max_size']; ?></span>
                    </div>
                </div>
                
                <div class="info-card">
                    <h4>File System Status</h4>
                    <div class="info-item">
                        <span class="info-label">Data Directory:</span>
                        <span class="info-value <?php echo $file_info['data_dir_writable'] ? 'status-good' : 'status-error'; ?>">
                            <?php echo $file_info['data_dir_writable'] ? '✓ Writable' : '✗ Not writable'; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Cache Directory:</span>
                        <span class="info-value <?php echo $file_info['cache_dir_writable'] ? 'status-good' : 'status-warning'; ?>">
                            <?php echo $file_info['cache_dir_writable'] ? '✓ Writable' : '⚠ Not writable'; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Users File:</span>
                        <span class="info-value"><?php echo number_format($file_info['users_file_size']); ?> bytes</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Withdrawals File:</span>
                        <span class="info-value"><?php echo number_format($file_info['withdrawals_file_size']); ?> bytes</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Actions -->
        <div class="content-section">
            <h3>🛠️ System Actions</h3>
            <div class="actions-grid">
                <div class="action-card">
                    <h4>🗑️ Clear Cache</h4>
                    <p>Remove all cached files to free up space and force data refresh.</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_cache">
                        <button type="submit" class="btn btn-warning" onclick="return confirm('Clear all cache files?')">
                            Clear Cache
                        </button>
                    </form>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        Cache files: <?php echo count($cache_files); ?>
                    </div>
                </div>
                
                <div class="action-card">
                    <h4>💾 Backup Data</h4>
                    <p>Create a backup of all important data files.</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="backup_data">
                        <button type="submit" class="btn btn-success" onclick="return confirm('Create data backup?')">
                            Create Backup
                        </button>
                    </form>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        Backups: <?php echo count($backup_files); ?>
                    </div>
                </div>
                
                <div class="action-card">
                    <h4>📊 System Status</h4>
                    <p>Overall system health and status.</p>
                    <div style="margin-top: 10px;">
                        <?php if ($file_info['data_dir_writable'] && $file_info['cache_dir_writable']): ?>
                            <span class="status-good">✓ System Healthy</span>
                        <?php elseif ($file_info['data_dir_writable']): ?>
                            <span class="status-warning">⚠ Cache Issues</span>
                        <?php else: ?>
                            <span class="status-error">✗ System Issues</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="action-card">
                    <h4>🔧 Maintenance</h4>
                    <p>System maintenance and optimization tools.</p>
                    <div style="margin-top: 10px;">
                        <?php if ($current_settings['maintenance_mode']): ?>
                            <span class="status-warning">⚠ Maintenance Mode ON</span>
                        <?php else: ?>
                            <span class="status-good">✓ System Online</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Backup Files -->
        <?php if (!empty($backup_files)): ?>
            <div class="content-section">
                <h3>💾 Backup Files</h3>
                <div class="file-list">
                    <?php foreach ($backup_files as $backup): ?>
                        <div class="file-item">
                            <div class="file-info">
                                <strong><?php echo htmlspecialchars($backup['name']); ?></strong>
                                <div class="file-size">
                                    <?php echo number_format($backup['size']); ?> bytes • 
                                    <?php echo $backup['date']; ?>
                                </div>
                            </div>
                            <div>
                                <a href="backups/<?php echo htmlspecialchars($backup['name']); ?>" class="btn" download>
                                    📥 Download
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Cache Files -->
        <?php if (!empty($cache_files)): ?>
            <div class="content-section">
                <h3>🗂️ Cache Files</h3>
                <div class="file-list">
                    <?php foreach ($cache_files as $cache): ?>
                        <div class="file-item">
                            <div class="file-info">
                                <strong><?php echo htmlspecialchars($cache['name']); ?></strong>
                                <div class="file-size">
                                    <?php echo number_format($cache['size']); ?> bytes • 
                                    <?php echo $cache['date']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Quick Links -->
        <div class="content-section">
            <h3>🔗 Quick Links</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="working_test.php" class="btn">🧪 System Test</a>
                <a href="hosting_check.php" class="btn">🖥️ Hosting Check</a>
                <a href="debug.php" class="btn">🔍 Debug Info</a>
                <a href="view_errors.php" class="btn">📋 Error Logs</a>
                <a href="financial_analytics.php" class="btn btn-success">💰 Financial Analytics</a>
                <a href="user_management.php" class="btn">👥 User Management</a>
            </div>
        </div>
    </div>
</body>
</html>
