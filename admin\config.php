<?php
// Admin Panel Configuration
// Optimized for <PERSON>inger shared hosting

// Start session with error handling
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

date_default_timezone_set('Asia/Kolkata');

// Error reporting for debugging (PHP 8.2 & <PERSON><PERSON> compatible)
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/admin_debug.log');
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING & ~E_DEPRECATED);
ini_set('display_errors', 0);

// PHP 8.2 compatibility settings
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 60);

// Check if main bot config exists and include it safely
$main_config_path = __DIR__ . '/../data/config.php';
if (file_exists($main_config_path)) {
    try {
        require_once $main_config_path;
    } catch (Exception $e) {
        // If main config fails, use fallback settings
        error_log("Failed to load main config: " . $e->getMessage());

        // Fallback configuration
        if (!defined('STORAGE_MODE')) {
            define('STORAGE_MODE', 'json'); // Default to JSON mode
        }
        if (!defined('DATA_DIR')) {
            define('DATA_DIR', __DIR__ . '/../data/');
        }
    }
} else {
    // Main config not found, use fallback
    define('STORAGE_MODE', 'json');
    define('DATA_DIR', __DIR__ . '/../data/');

    // Create basic data directory if it doesn't exist
    if (!is_dir(DATA_DIR)) {
        @mkdir(DATA_DIR, 0755, true);
    }
}

// Admin Panel Configuration
define('ADMIN_PASSCODE', '1412');
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
define('ADMIN_PANEL_VERSION', '1.0.0');

// Pagination settings for performance
define('DEFAULT_PAGE_SIZE', 50);
define('MAX_PAGE_SIZE', 200);

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 300); // 5 minutes
define('CACHE_DIR', __DIR__ . '/cache/');

// Performance settings
define('QUERY_TIMEOUT', 30); // seconds
define('MAX_MEMORY_LIMIT', '256M');

// Create cache directory if it doesn't exist
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

// Set memory limit for large datasets
ini_set('memory_limit', MAX_MEMORY_LIMIT);

// Database connection function optimized for admin panel (Hostinger compatible)
function getAdminDB() {
    static $pdo = null;

    if ($pdo === null) {
        try {
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => QUERY_TIMEOUT
            ];

            if (STORAGE_MODE === 'mysql' && defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                // Add MySQL specific options only if we're using MySQL
                $options[PDO::MYSQL_ATTR_INIT_COMMAND] = "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'";

                $pdo = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    $options
                );
            } else {
                // For JSON mode or if MySQL not configured, use SQLite for admin panel caching
                $sqlite_path = CACHE_DIR . 'admin_cache.db';
                $pdo = new PDO('sqlite:' . $sqlite_path, null, null, $options);
                initializeSQLiteCache($pdo);
            }
        } catch (PDOException $e) {
            error_log("Admin DB Connection Error: " . $e->getMessage());
            // Don't die immediately, return null and handle gracefully
            return null;
        }
    }

    return $pdo;
}

// Initialize SQLite cache for JSON mode (shared hosting compatible)
function initializeSQLiteCache($pdo) {
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS cache_stats (
            cache_key TEXT PRIMARY KEY,
            cache_data TEXT,
            created_at INTEGER,
            expires_at INTEGER
        )");
        return true;
    } catch (PDOException $e) {
        error_log("SQLite cache initialization failed: " . $e->getMessage());
        return false;
    }
}

// Cache management functions
function getCachedData($key) {
    if (!CACHE_ENABLED) return null;
    
    $cacheFile = CACHE_DIR . md5($key) . '.cache';
    if (file_exists($cacheFile)) {
        $data = unserialize(file_get_contents($cacheFile));
        if ($data['expires'] > time()) {
            return $data['content'];
        } else {
            unlink($cacheFile);
        }
    }
    return null;
}

function setCachedData($key, $data, $duration = CACHE_DURATION) {
    if (!CACHE_ENABLED) return;
    
    $cacheFile = CACHE_DIR . md5($key) . '.cache';
    $cacheData = [
        'content' => $data,
        'expires' => time() + $duration
    ];
    file_put_contents($cacheFile, serialize($cacheData));
}

// Authentication functions
function isAuthenticated() {
    return isset($_SESSION['admin_authenticated']) && 
           $_SESSION['admin_authenticated'] === true &&
           isset($_SESSION['admin_login_time']) &&
           (time() - $_SESSION['admin_login_time']) < ADMIN_SESSION_TIMEOUT;
}

function requireAuth() {
    if (!isAuthenticated()) {
        header('Location: login.php');
        exit;
    }
}

function authenticate($passcode) {
    if ($passcode === ADMIN_PASSCODE) {
        $_SESSION['admin_authenticated'] = true;
        $_SESSION['admin_login_time'] = time();
        return true;
    }
    return false;
}

function logout() {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Utility functions
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

function formatNumber($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

// Pagination helper
function getPaginationData($page, $totalRecords, $pageSize = DEFAULT_PAGE_SIZE) {
    $page = max(1, intval($page));
    $pageSize = min(MAX_PAGE_SIZE, max(1, intval($pageSize)));
    $totalPages = ceil($totalRecords / $pageSize);
    $offset = ($page - 1) * $pageSize;
    
    return [
        'page' => $page,
        'pageSize' => $pageSize,
        'totalRecords' => $totalRecords,
        'totalPages' => $totalPages,
        'offset' => $offset,
        'hasNext' => $page < $totalPages,
        'hasPrev' => $page > 1
    ];
}

// Security headers
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
}

// Initialize security headers
setSecurityHeaders();
?>
