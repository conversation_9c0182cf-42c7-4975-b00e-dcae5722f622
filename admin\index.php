<?php
// Admin Panel Entry Point (PHP 8.2 compatible)
try {
    // Try to load config safely
    if (file_exists(__DIR__ . '/config.php')) {
        require_once 'config.php';

        // Check if functions are available
        if (function_exists('isAuthenticated')) {
            // Redirect to dashboard if authenticated, otherwise to login
            if (isAuthenticated()) {
                header('Location: dashboard.php');
            } else {
                header('Location: login.php');
            }
            exit;
        } else {
            // Functions not available, redirect to simple login
            header('Location: simple_login.php');
            exit;
        }
    } else {
        // Config not found, redirect to simple login
        header('Location: simple_login.php');
        exit;
    }
} catch (Exception $e) {
    // If anything fails, redirect to simple login
    header('Location: simple_login.php');
    exit;
}
?>
