<?php
// Shared Hosting Compatibility Checker
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hosting Compatibility Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .check-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
        h1, h2 { color: #333; }
        .status-good { background: #d4edda; border-left: 4px solid #28a745; }
        .status-warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status-error { background: #f8d7da; border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Shared Hosting Compatibility Check</h1>
        <p>This tool checks if your hosting environment is compatible with the admin panel.</p>
        
        <div class="section">
            <h2>📊 Environment Information</h2>
            <div class="check-item">
                <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?>
                <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                    <span class="success">✓ Compatible</span>
                <?php else: ?>
                    <span class="error">✗ Requires PHP 7.4+</span>
                <?php endif; ?>
            </div>
            
            <div class="check-item">
                <strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
            </div>
            
            <div class="check-item">
                <strong>Operating System:</strong> <?php echo PHP_OS; ?>
            </div>
            
            <div class="check-item">
                <strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?>
                <?php 
                $memory_limit = ini_get('memory_limit');
                $memory_bytes = 0;
                if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
                    $memory_bytes = $matches[1];
                    if ($matches[2] == 'M') $memory_bytes *= 1024 * 1024;
                    if ($matches[2] == 'G') $memory_bytes *= 1024 * 1024 * 1024;
                }
                if ($memory_bytes >= 128 * 1024 * 1024): ?>
                    <span class="success">✓ Sufficient</span>
                <?php else: ?>
                    <span class="warning">⚠ May be low for large datasets</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 Required PHP Extensions</h2>
            <?php
            $required_extensions = [
                'pdo' => 'Database connectivity',
                'pdo_mysql' => 'MySQL database support',
                'json' => 'JSON data handling',
                'session' => 'Session management',
                'mbstring' => 'Multi-byte string handling',
                'openssl' => 'Security functions'
            ];
            
            $all_extensions_ok = true;
            foreach ($required_extensions as $ext => $description): ?>
                <div class="check-item <?php echo extension_loaded($ext) ? 'status-good' : 'status-error'; ?>">
                    <strong><?php echo $ext; ?>:</strong> <?php echo $description; ?>
                    <?php if (extension_loaded($ext)): ?>
                        <span class="success">✓ Available</span>
                    <?php else: ?>
                        <span class="error">✗ Missing</span>
                        <?php $all_extensions_ok = false; ?>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section">
            <h2>🚫 Disabled Functions (Common on Shared Hosting)</h2>
            <p>These functions are typically disabled on shared hosting for security. The admin panel is designed to work without them.</p>
            <?php
            $potentially_disabled = [
                'exec' => 'Execute system commands',
                'shell_exec' => 'Execute shell commands',
                'system' => 'Execute system programs',
                'passthru' => 'Execute external programs',
                'proc_open' => 'Execute processes',
                'popen' => 'Open process file pointer'
            ];
            
            foreach ($potentially_disabled as $func => $description): ?>
                <div class="check-item <?php echo function_exists($func) ? 'status-warning' : 'status-good'; ?>">
                    <strong><?php echo $func; ?>():</strong> <?php echo $description; ?>
                    <?php if (function_exists($func)): ?>
                        <span class="warning">⚠ Available (unusual on shared hosting)</span>
                    <?php else: ?>
                        <span class="success">✓ Disabled (normal and secure)</span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section">
            <h2>📁 File System Permissions</h2>
            <?php
            $directories_to_check = [
                '.' => 'Admin directory',
                'cache' => 'Cache directory',
                'includes' => 'Includes directory'
            ];
            
            foreach ($directories_to_check as $dir => $description): 
                $path = __DIR__ . '/' . $dir;
                if ($dir === '.') $path = __DIR__;
                ?>
                <div class="check-item <?php echo is_writable($path) ? 'status-good' : 'status-warning'; ?>">
                    <strong><?php echo $dir; ?>/:</strong> <?php echo $description; ?>
                    <?php if (is_dir($path)): ?>
                        <?php if (is_writable($path)): ?>
                            <span class="success">✓ Writable</span>
                        <?php else: ?>
                            <span class="warning">⚠ Not writable</span>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="error">✗ Directory missing</span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="section">
            <h2>🔗 Configuration Test</h2>
            <?php
            $config_status = 'unknown';
            $config_message = '';
            
            try {
                if (file_exists(__DIR__ . '/config.php')) {
                    ob_start();
                    include_once __DIR__ . '/config.php';
                    ob_end_clean();
                    
                    if (defined('ADMIN_PASSCODE')) {
                        $config_status = 'good';
                        $config_message = 'Configuration loaded successfully';
                    } else {
                        $config_status = 'warning';
                        $config_message = 'Configuration loaded but some constants missing';
                    }
                } else {
                    $config_status = 'error';
                    $config_message = 'Configuration file not found';
                }
            } catch (Exception $e) {
                $config_status = 'error';
                $config_message = 'Configuration error: ' . $e->getMessage();
            }
            ?>
            
            <div class="check-item status-<?php echo $config_status; ?>">
                <strong>Configuration Status:</strong>
                <?php if ($config_status === 'good'): ?>
                    <span class="success">✓ <?php echo $config_message; ?></span>
                <?php elseif ($config_status === 'warning'): ?>
                    <span class="warning">⚠ <?php echo $config_message; ?></span>
                <?php else: ?>
                    <span class="error">✗ <?php echo $config_message; ?></span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section">
            <h2>🎯 Overall Compatibility</h2>
            <?php
            $compatibility_score = 0;
            $max_score = 0;
            
            // PHP Version check
            $max_score++;
            if (version_compare(PHP_VERSION, '7.4.0', '>=')) $compatibility_score++;
            
            // Extensions check
            foreach ($required_extensions as $ext => $desc) {
                $max_score++;
                if (extension_loaded($ext)) $compatibility_score++;
            }
            
            // Config check
            $max_score++;
            if ($config_status === 'good') $compatibility_score++;
            
            $compatibility_percentage = ($compatibility_score / $max_score) * 100;
            ?>
            
            <div class="check-item <?php echo $compatibility_percentage >= 90 ? 'status-good' : ($compatibility_percentage >= 70 ? 'status-warning' : 'status-error'); ?>">
                <strong>Compatibility Score:</strong> <?php echo $compatibility_score; ?>/<?php echo $max_score; ?> (<?php echo round($compatibility_percentage); ?>%)
                
                <?php if ($compatibility_percentage >= 90): ?>
                    <span class="success">✓ Excellent - Admin panel should work perfectly</span>
                <?php elseif ($compatibility_percentage >= 70): ?>
                    <span class="warning">⚠ Good - Admin panel should work with minor limitations</span>
                <?php else: ?>
                    <span class="error">✗ Poor - Some features may not work properly</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section">
            <h2>🚀 Next Steps</h2>
            <?php if ($compatibility_percentage >= 70): ?>
                <p class="success">✅ Your hosting environment is compatible! Try these links:</p>
                <ul>
                    <li><a href="simple_login.php">Simple Login</a> - Basic admin panel</li>
                    <li><a href="login.php">Full Admin Panel</a> - Complete admin interface</li>
                    <li><a href="debug.php">Debug Information</a> - Detailed system info</li>
                </ul>
            <?php else: ?>
                <p class="warning">⚠️ Some compatibility issues detected:</p>
                <ul>
                    <li>Contact your hosting provider about missing PHP extensions</li>
                    <li>Try the <a href="simple_login.php">Simple Login</a> for basic functionality</li>
                    <li>Run <a href="fix_permissions.php">Permission Fix</a> to resolve file issues</li>
                </ul>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>📞 Support Information</h2>
            <p><strong>Hosting Provider:</strong> Hostinger (detected from server signature)</p>
            <p><strong>Recommended Actions:</strong></p>
            <ul>
                <li>✅ Disabled system functions (exec, shell_exec) - This is normal and secure</li>
                <li>✅ PHP 8.2 compatibility - Admin panel is fully compatible</li>
                <li>✅ LiteSpeed server - Optimized for performance</li>
            </ul>
        </div>
    </div>
</body>
</html>
