<?php
session_start();

// Check login
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: super_simple_login.php');
    exit;
}

// Load data
$users = [];
$withdrawal_reports = [];
$error = '';

try {
    // Load users
    $users_file = __DIR__ . '/../data/users.json';
    if (file_exists($users_file)) {
        $content = file_get_contents($users_file);
        if ($content) {
            $users = json_decode($content, true);
        }
    }
    
    // Load withdrawal reports
    $withdrawals_file = __DIR__ . '/../data/withdrawal_reports.json';
    if (file_exists($withdrawals_file)) {
        $content = file_get_contents($withdrawals_file);
        if ($content) {
            $withdrawal_reports = json_decode($content, true);
            if (!is_array($withdrawal_reports)) {
                $withdrawal_reports = [];
            }
        }
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Calculate financial metrics
$metrics = [
    'total_user_balance' => 0,
    'total_withdrawals' => 0,
    'pending_withdrawals' => 0,
    'total_referral_earnings' => 0,
    'total_joining_bonus' => 0,
    'platform_revenue' => 0,
    'active_users' => 0,
    'high_balance_users' => 0,
    'withdrawal_requests' => 0,
    'avg_user_balance' => 0,
    'avg_withdrawal' => 0,
    'withdrawal_success_rate' => 0
];

// Process users data
foreach ($users as $user_id => $user) {
    $balance = $user['balance'] ?? 0;
    $successful_withdraw = $user['successful_withdraw'] ?? 0;
    $pending_withdraw = $user['withdraw_under_review'] ?? 0;
    $joining_bonus = $user['joining_bonus_got'] ?? 0;
    
    $metrics['total_user_balance'] += $balance;
    $metrics['total_withdrawals'] += $successful_withdraw;
    $metrics['pending_withdrawals'] += $pending_withdraw;
    $metrics['total_joining_bonus'] += $joining_bonus;
    
    if ($balance > 0) {
        $metrics['active_users']++;
    }
    
    if ($balance >= 1000) {
        $metrics['high_balance_users']++;
    }
    
    // Calculate referral earnings
    if (isset($user['promotion_report']) && is_array($user['promotion_report'])) {
        foreach ($user['promotion_report'] as $referral) {
            $metrics['total_referral_earnings'] += $referral['amount_got'] ?? 0;
        }
    }
}

// Process withdrawal reports
$completed_withdrawals = 0;
$total_withdrawal_requests = count($withdrawal_reports);

foreach ($withdrawal_reports as $withdrawal) {
    if ($withdrawal['status'] === 'Completed') {
        $completed_withdrawals++;
    }
}

$metrics['withdrawal_requests'] = $total_withdrawal_requests;
$metrics['withdrawal_success_rate'] = $total_withdrawal_requests > 0 ? 
    ($completed_withdrawals / $total_withdrawal_requests) * 100 : 0;

// Calculate averages
$total_users = count($users);
$metrics['avg_user_balance'] = $total_users > 0 ? $metrics['total_user_balance'] / $total_users : 0;
$metrics['avg_withdrawal'] = $completed_withdrawals > 0 ? $metrics['total_withdrawals'] / $completed_withdrawals : 0;

// Calculate platform revenue (estimated)
$metrics['platform_revenue'] = $metrics['total_referral_earnings'] * 0.1; // Assuming 10% platform fee

// Monthly breakdown (last 12 months)
$monthly_data = [];
$current_month = date('Y-m');

for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $monthly_data[$month] = [
        'month' => date('M Y', strtotime("-$i months")),
        'new_users' => 0,
        'withdrawals' => 0,
        'referrals' => 0,
        'revenue' => 0
    ];
}

// Process monthly data
foreach ($users as $user_id => $user) {
    $created_month = date('Y-m', strtotime($user['created_at'] ?? '2024-01-01'));
    if (isset($monthly_data[$created_month])) {
        $monthly_data[$created_month]['new_users']++;
    }
}

foreach ($withdrawal_reports as $withdrawal) {
    $withdrawal_month = date('Y-m', strtotime($withdrawal['date'] ?? '2024-01-01'));
    if (isset($monthly_data[$withdrawal_month]) && $withdrawal['status'] === 'Completed') {
        $monthly_data[$withdrawal_month]['withdrawals'] += $withdrawal['amount'] ?? 0;
    }
}

// Top users by different metrics
$top_users = [
    'balance' => [],
    'withdrawals' => [],
    'referrals' => []
];

foreach ($users as $user_id => $user) {
    $user_data = [
        'user_id' => $user_id,
        'name' => $user['first_name'] ?? 'Unknown',
        'balance' => $user['balance'] ?? 0,
        'withdrawals' => $user['successful_withdraw'] ?? 0,
        'referrals' => count($user['promotion_report'] ?? [])
    ];
    
    $top_users['balance'][] = $user_data;
    $top_users['withdrawals'][] = $user_data;
    $top_users['referrals'][] = $user_data;
}

// Sort top users
usort($top_users['balance'], function($a, $b) { return $b['balance'] - $a['balance']; });
usort($top_users['withdrawals'], function($a, $b) { return $b['withdrawals'] - $a['withdrawals']; });
usort($top_users['referrals'], function($a, $b) { return $b['referrals'] - $a['referrals']; });

// Limit to top 10
$top_users['balance'] = array_slice($top_users['balance'], 0, 10);
$top_users['withdrawals'] = array_slice($top_users['withdrawals'], 0, 10);
$top_users['referrals'] = array_slice($top_users['referrals'], 0, 10);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Financial Analytics</title>
    <style>
        body { font-family: Arial; margin: 0; background: #f5f5f5; }
        .header { background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { padding: 20px; max-width: 1400px; margin: 0 auto; }
        .back-btn { background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 10px; }
        .metric-label { color: #666; font-size: 0.9em; text-transform: uppercase; letter-spacing: 1px; }
        .metric-change { font-size: 0.8em; margin-top: 5px; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        .revenue { color: #28a745; }
        .balance { color: #007bff; }
        .withdrawal { color: #17a2b8; }
        .referral { color: #6f42c1; }
        .content-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .charts-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .chart-placeholder { height: 300px; background: #f8f9fa; border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center; color: #6c757d; border-radius: 8px; }
        .top-users-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .user-list { max-height: 400px; overflow-y: auto; }
        .user-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .user-item:hover { background: #f8f9fa; }
        .user-info { flex: 1; }
        .user-value { font-weight: bold; color: #007bff; }
        .summary-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .summary-item { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .summary-item strong { display: block; font-size: 1.2em; color: #333; margin-bottom: 5px; }
        .message { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .message.error { background: #f8d7da; color: #721c24; }
        .message.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Financial Analytics</h1>
        <a href="basic_admin.php" class="back-btn">← Back to Dashboard</a>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="message error">❌ Error: <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Key Financial Metrics -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value revenue">₹<?php echo number_format($metrics['total_user_balance'], 2); ?></div>
                <div class="metric-label">Total User Balance</div>
                <div class="metric-change neutral">Active funds in system</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value withdrawal">₹<?php echo number_format($metrics['total_withdrawals'], 2); ?></div>
                <div class="metric-label">Total Withdrawals</div>
                <div class="metric-change positive">Successfully paid out</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value referral">₹<?php echo number_format($metrics['total_referral_earnings'], 2); ?></div>
                <div class="metric-label">Referral Earnings</div>
                <div class="metric-change positive">Commission generated</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value revenue">₹<?php echo number_format($metrics['platform_revenue'], 2); ?></div>
                <div class="metric-label">Platform Revenue</div>
                <div class="metric-change positive">Estimated earnings</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value balance">₹<?php echo number_format($metrics['pending_withdrawals'], 2); ?></div>
                <div class="metric-label">Pending Withdrawals</div>
                <div class="metric-change neutral"><?php echo $metrics['withdrawal_requests']; ?> requests</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value neutral"><?php echo number_format($metrics['withdrawal_success_rate'], 1); ?>%</div>
                <div class="metric-label">Withdrawal Success Rate</div>
                <div class="metric-change positive">Processing efficiency</div>
            </div>
        </div>
        
        <!-- Summary Statistics -->
        <div class="content-section">
            <h3>📈 Summary Statistics</h3>
            <div class="summary-stats">
                <div class="summary-item">
                    <strong><?php echo number_format($total_users); ?></strong>
                    <div>Total Users</div>
                </div>
                <div class="summary-item">
                    <strong><?php echo number_format($metrics['active_users']); ?></strong>
                    <div>Active Users</div>
                </div>
                <div class="summary-item">
                    <strong><?php echo number_format($metrics['high_balance_users']); ?></strong>
                    <div>High Balance Users (₹1000+)</div>
                </div>
                <div class="summary-item">
                    <strong>₹<?php echo number_format($metrics['avg_user_balance'], 2); ?></strong>
                    <div>Average User Balance</div>
                </div>
                <div class="summary-item">
                    <strong>₹<?php echo number_format($metrics['avg_withdrawal'], 2); ?></strong>
                    <div>Average Withdrawal</div>
                </div>
                <div class="summary-item">
                    <strong>₹<?php echo number_format($metrics['total_joining_bonus'], 2); ?></strong>
                    <div>Total Joining Bonuses</div>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="charts-grid">
            <div class="chart-container">
                <h3>📊 Monthly Financial Trends</h3>
                <div class="chart-placeholder">
                    <div style="text-align: center;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📈 Monthly Trends Chart</div>
                        <div>Interactive chart showing:</div>
                        <ul style="text-align: left; margin-top: 10px;">
                            <li>New user registrations</li>
                            <li>Withdrawal amounts</li>
                            <li>Revenue trends</li>
                            <li>Growth patterns</li>
                        </ul>
                        <div style="margin-top: 15px; font-size: 0.9em; color: #999;">
                            Chart.js integration available for enhanced visualization
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>🥧 Revenue Distribution</h3>
                <div class="chart-placeholder">
                    <div style="text-align: center;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">🥧 Distribution Chart</div>
                        <div>Revenue breakdown:</div>
                        <ul style="text-align: left; margin-top: 10px;">
                            <li>User Balances: <?php echo number_format(($metrics['total_user_balance'] / max(1, $metrics['total_user_balance'] + $metrics['total_withdrawals'])) * 100, 1); ?>%</li>
                            <li>Withdrawals: <?php echo number_format(($metrics['total_withdrawals'] / max(1, $metrics['total_user_balance'] + $metrics['total_withdrawals'])) * 100, 1); ?>%</li>
                            <li>Platform Revenue: ₹<?php echo number_format($metrics['platform_revenue'], 2); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Top Users -->
        <div class="content-section">
            <h3>🏆 Top Performers</h3>
            <div class="top-users-grid">
                <!-- Top by Balance -->
                <div>
                    <h4>💰 Highest Balances</h4>
                    <div class="user-list">
                        <?php foreach ($top_users['balance'] as $index => $user): ?>
                            <div class="user-item">
                                <div class="user-info">
                                    <strong>#<?php echo $index + 1; ?> <?php echo htmlspecialchars($user['name']); ?></strong>
                                    <br><small>ID: <?php echo htmlspecialchars($user['user_id']); ?></small>
                                </div>
                                <div class="user-value">₹<?php echo number_format($user['balance'], 2); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Top by Withdrawals -->
                <div>
                    <h4>💸 Highest Withdrawals</h4>
                    <div class="user-list">
                        <?php foreach ($top_users['withdrawals'] as $index => $user): ?>
                            <div class="user-item">
                                <div class="user-info">
                                    <strong>#<?php echo $index + 1; ?> <?php echo htmlspecialchars($user['name']); ?></strong>
                                    <br><small>ID: <?php echo htmlspecialchars($user['user_id']); ?></small>
                                </div>
                                <div class="user-value">₹<?php echo number_format($user['withdrawals'], 2); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Top by Referrals -->
                <div>
                    <h4>🔗 Most Referrals</h4>
                    <div class="user-list">
                        <?php foreach ($top_users['referrals'] as $index => $user): ?>
                            <div class="user-item">
                                <div class="user-info">
                                    <strong>#<?php echo $index + 1; ?> <?php echo htmlspecialchars($user['name']); ?></strong>
                                    <br><small>ID: <?php echo htmlspecialchars($user['user_id']); ?></small>
                                </div>
                                <div class="user-value"><?php echo number_format($user['referrals']); ?> refs</div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Financial Health Indicators -->
        <div class="content-section">
            <h3>🏥 Financial Health Indicators</h3>
            <div class="summary-stats">
                <div class="summary-item">
                    <strong style="color: <?php echo $metrics['withdrawal_success_rate'] > 90 ? '#28a745' : ($metrics['withdrawal_success_rate'] > 70 ? '#ffc107' : '#dc3545'); ?>">
                        <?php echo number_format($metrics['withdrawal_success_rate'], 1); ?>%
                    </strong>
                    <div>Withdrawal Success Rate</div>
                </div>
                <div class="summary-item">
                    <strong style="color: <?php echo $metrics['active_users'] / max(1, $total_users) > 0.5 ? '#28a745' : '#ffc107'; ?>">
                        <?php echo number_format(($metrics['active_users'] / max(1, $total_users)) * 100, 1); ?>%
                    </strong>
                    <div>User Activity Rate</div>
                </div>
                <div class="summary-item">
                    <strong style="color: #007bff;">
                        <?php echo number_format(($metrics['total_withdrawals'] / max(1, $metrics['total_user_balance'] + $metrics['total_withdrawals'])) * 100, 1); ?>%
                    </strong>
                    <div>Withdrawal Ratio</div>
                </div>
                <div class="summary-item">
                    <strong style="color: #6f42c1;">
                        ₹<?php echo number_format($metrics['total_referral_earnings'] / max(1, $total_users), 2); ?>
                    </strong>
                    <div>Avg Referral Earnings per User</div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="content-section">
            <h3>⚡ Quick Actions</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="withdrawals.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    💰 Manage Withdrawals
                </a>
                <a href="user_management.php?filter=high_balance" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    👥 View High Balance Users
                </a>
                <a href="referral_analytics.php" style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    📈 Referral Analytics
                </a>
                <a href="system_admin.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    ⚙️ System Settings
                </a>
            </div>
        </div>
    </div>
</body>
</html>
